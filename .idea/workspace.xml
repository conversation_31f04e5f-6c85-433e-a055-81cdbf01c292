<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="1eeb2edd-cc76-407d-9984-507a007f7828" name="Default" comment="">
<<<<<<< HEAD
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" afterPath="$PROJECT_DIR$/.idea/workspace.xml" />
=======
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/.idea/workspace.xml" afterPath="$PROJECT_DIR$/.idea/workspace.xml" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
    </list>
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="Authority.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/page/setting/Authority.vue">
          <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
            <state relative-caret-position="1216">
=======
            <state relative-caret-position="0">
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
              <caret line="64" column="86" lean-forward="false" selection-start-line="64" selection-start-column="20" selection-end-line="64" selection-end-column="86" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Customer.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/page/examine/Customer.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="3" column="62" lean-forward="false" selection-start-line="3" selection-start-column="62" selection-end-line="3" selection-end-column="62" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Home.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/common/Home.vue">
          <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
            <state relative-caret-position="57">
              <caret line="3" column="62" lean-forward="false" selection-start-line="3" selection-start-column="62" selection-end-line="3" selection-end-column="62" />
              <folding />
=======
            <state relative-caret-position="220">
              <caret line="34" column="20" lean-forward="false" selection-start-line="34" selection-start-column="20" selection-end-line="38" selection-end-column="15" />
              <folding>
                <element signature="e#1201#1234#0" expanded="true" />
              </folding>
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="index.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/router/index.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="110" column="0" lean-forward="false" selection-start-line="110" selection-start-column="0" selection-end-line="110" selection-end-column="0" />
              <folding>
                <element signature="e#0#22#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Loading.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/common/Loading.vue">
          <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
            <state relative-caret-position="36">
              <caret line="178" column="20" lean-forward="false" selection-start-line="178" selection-start-column="20" selection-end-line="178" selection-end-column="20" />
=======
            <state relative-caret-position="0">
              <caret line="17" column="10" lean-forward="false" selection-start-line="17" selection-start-column="10" selection-end-line="17" selection-end-column="10" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="package.json" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="252">
              <caret line="62" column="4" lean-forward="false" selection-start-line="62" selection-start-column="4" selection-end-line="62" selection-end-column="4" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Sidebar.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/common/Sidebar.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="105" column="56" lean-forward="false" selection-start-line="105" selection-start-column="48" selection-end-line="105" selection-end-column="56" />
              <folding>
                <element signature="e#11196#13125#0" expanded="false" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="tip" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/tip">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="2" column="66" lean-forward="false" selection-start-line="2" selection-start-column="66" selection-end-line="2" selection-end-column="66" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Transport.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/page/examine/Transport.vue">
          <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
            <state relative-caret-position="1995">
              <caret line="105" column="56" lean-forward="false" selection-start-line="105" selection-start-column="48" selection-end-line="105" selection-end-column="56" />
              <folding>
                <element signature="e#11196#13125#0" expanded="false" />
              </folding>
=======
            <state relative-caret-position="0">
              <caret line="11" column="30" lean-forward="false" selection-start-line="11" selection-start-column="30" selection-end-line="11" selection-end-column="30" />
              <folding />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Welcome.vue" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/components/page/public/Welcome.vue">
          <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
            <state relative-caret-position="2071">
              <caret line="110" column="0" lean-forward="true" selection-start-line="110" selection-start-column="0" selection-end-line="110" selection-end-column="0" />
              <folding>
                <element signature="e#0#22#0" expanded="true" />
              </folding>
=======
            <state relative-caret-position="0">
              <caret line="15" column="0" lean-forward="false" selection-start-line="15" selection-start-column="0" selection-end-line="15" selection-end-column="0" />
              <folding />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="JavaScript File" />
        <option value="Vue Single File Component" />
        <option value="Less File" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>name</find>
      <find>.js</find>
      <find>transition</find>
      <find>.css</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/util/eventBus.js" />
        <option value="$PROJECT_DIR$/src/components/common/loading.html" />
        <option value="$PROJECT_DIR$/src/components/common/loading.vue" />
        <option value="$PROJECT_DIR$/src/util/vuex.js" />
        <option value="$PROJECT_DIR$/src/util/serve.js" />
        <option value="$PROJECT_DIR$/src/components/common/Loading.vue" />
        <option value="$PROJECT_DIR$/dist/index.html" />
        <option value="$PROJECT_DIR$/src/components/page/Login.vue" />
        <option value="$PROJECT_DIR$/static/css/main.css" />
        <option value="$PROJECT_DIR$/src/components/page/public/test.vue" />
        <option value="$PROJECT_DIR$/src/components/page/public/Login.vue" />
        <option value="$PROJECT_DIR$/src/components/common/Header.vue" />
        <option value="$PROJECT_DIR$/src/App.vue" />
        <option value="$PROJECT_DIR$/src/main.js" />
        <option value="$PROJECT_DIR$/index.html" />
        <option value="$PROJECT_DIR$/static/css/base.less" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Authority-edit.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Authority.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Employee-edit.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Material-edit.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Material.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Message.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Employee.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Customer.vue" />
        <option value="$PROJECT_DIR$/src/components/page/setting/Message-new.vue" />
        <option value="$PROJECT_DIR$/src/components/common/Sidebar.vue" />
        <option value="$PROJECT_DIR$/tip" />
        <option value="$PROJECT_DIR$/src/router/index.js" />
        <option value="$PROJECT_DIR$/src/components/page/public/Welcome.vue" />
        <option value="$PROJECT_DIR$/src/components/common/Home.vue" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
<<<<<<< HEAD
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-19" />
    <option name="y" value="46" />
    <option name="width" value="2030" />
    <option name="height" value="981" />
=======
  <component name="ProjectFrameBounds" extendedState="1">
    <option name="x" value="22" />
    <option name="y" value="79" />
    <option name="width" value="1438" />
    <option name="height" value="794" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="build" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="config" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="assets" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
              <item name="common" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
              <item name="page" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
              <item name="page" type="462c0819:PsiDirectoryNode" />
              <item name="examine" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
              <item name="page" type="462c0819:PsiDirectoryNode" />
              <item name="public" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
              <item name="page" type="462c0819:PsiDirectoryNode" />
              <item name="setting" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="router" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="util" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="static" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="static" type="462c0819:PsiDirectoryNode" />
              <item name="css" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="static" type="462c0819:PsiDirectoryNode" />
              <item name="css" type="462c0819:PsiDirectoryNode" />
              <item name="theme-green" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="kuangye" type="b2602c69:ProjectViewProjectNode" />
              <item name="kuangye" type="462c0819:PsiDirectoryNode" />
              <item name="static" type="462c0819:PsiDirectoryNode" />
              <item name="js" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
      <pane id="Scratches" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="settings.editor.selected.configurable" value="fileTemplates" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="HbShouldOpenHtmlAsHb" value="" />
    <property name="DefaultHtmlFileTemplate" value="HTML File" />
    <property name="list.type.of.created.stylesheet" value="Less" />
    <property name="nodejs_interpreter_path" value="/usr/local/bin/node" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/components/page/public" />
      <recent name="$PROJECT_DIR$/src/components/page/base" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/components/page/examine" />
      <recent name="$PROJECT_DIR$/src/components/page/setting" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" factoryName="npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
    </configuration>
    <recent_temporary>
      <list size="1">
        <item index="0" class="java.lang.String" itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1eeb2edd-cc76-407d-9984-507a007f7828" name="Default" comment="" />
      <created>1524205970171</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1524205970171</updated>
      <workItem from="1524205978622" duration="1847000" />
      <workItem from="1524541728869" duration="16492000" />
      <workItem from="1524619085080" duration="19394000" />
      <workItem from="1524704152812" duration="5406000" />
<<<<<<< HEAD
      <workItem from="1524792282875" duration="24018000" />
      <workItem from="1524878130064" duration="14000" />
=======
      <workItem from="1524792282875" duration="23696000" />
      <workItem from="1524833972748" duration="5176000" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
<<<<<<< HEAD
    <option name="totallyTimeSpent" value="67171000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="22" width="1920" height="981" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.13586098" sideWeight="0.7708934" order="0" side_tool="false" content_ui="combo" />
=======
    <option name="totallyTimeSpent" value="72011000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="22" y="79" width="1438" height="794" extended-state="1" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.20254058" sideWeight="0.7686703" order="0" side_tool="false" content_ui="combo" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="true" content_ui="tabs" />
<<<<<<< HEAD
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.2167043" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.13586098" sideWeight="0.22910663" order="2" side_tool="true" content_ui="tabs" />
=======
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.20254058" sideWeight="0.2313297" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.22174536" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.21459228" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="CSS Styles" active="true" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.3295711" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
<<<<<<< HEAD
=======
      <window_info id="Docker" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="false" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <option name="time" value="10" />
    </breakpoint-manager>
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/components/page/base/Readme.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-523">
          <caret line="44" column="25" lean-forward="true" selection-start-line="0" selection-start-column="0" selection-end-line="95" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/static/css/main.css">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="247">
          <caret line="13" column="13" lean-forward="false" selection-start-line="13" selection-start-column="13" selection-end-line="13" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/static/css/color-dark.css">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build/vue-loader.conf.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="247">
          <caret line="13" column="19" lean-forward="true" selection-start-line="13" selection-start-column="19" selection-end-line="13" selection-end-column="19" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/config/dev.env.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/config/prod.env.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/config/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-101">
          <caret line="14" column="22" lean-forward="false" selection-start-line="14" selection-start-column="22" selection-end-line="14" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build/utils.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-158">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build/dev-client.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build/dev-server.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="260">
          <caret line="64" column="27" lean-forward="false" selection-start-line="64" selection-start-column="18" selection-end-line="64" selection-end-column="27" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build/webpack.prod.conf.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1355">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build/check-versions.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/index.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="475">
          <caret line="25" column="0" lean-forward="false" selection-start-line="25" selection-start-column="0" selection-end-line="25" selection-end-column="0" />
<<<<<<< HEAD
=======
          <folding />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/static/css/app.67fb2d8a28a9c1296ebe25642c582b72.css" />
<<<<<<< HEAD
    <entry file="file://$PROJECT_DIR$/dist/static/css/color-dark.css">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/static/css/datasource.css">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/static/css/main.css">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1199">
          <caret line="20" column="8" lean-forward="true" selection-start-line="20" selection-start-column="8" selection-end-line="20" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/static/data.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="285">
          <caret line="15" column="21" lean-forward="true" selection-start-line="15" selection-start-column="21" selection-end-line="15" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/static/css/base.less">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="399">
          <caret line="21" column="26" lean-forward="false" selection-start-line="21" selection-start-column="26" selection-end-line="21" selection-end-column="26" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/index.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="95">
          <caret line="0" column="792" lean-forward="true" selection-start-line="0" selection-start-column="792" selection-end-line="0" selection-end-column="792" />
        </state>
      </provider>
    </entry>
=======
    <entry file="file://$PROJECT_DIR$/dist/static/css/color-dark.css" />
    <entry file="file://$PROJECT_DIR$/dist/static/css/datasource.css" />
    <entry file="file://$PROJECT_DIR$/dist/static/css/main.css" />
    <entry file="file://$PROJECT_DIR$/dist/static/data.json" />
    <entry file="file://$PROJECT_DIR$/dist/static/css/base.less" />
    <entry file="file://$PROJECT_DIR$/dist/index.html" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
    <entry file="file://$PROJECT_DIR$/src/components/page/base/Upload.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-627">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/public/Login.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="194">
          <caret line="41" column="10" lean-forward="true" selection-start-line="41" selection-start-column="10" selection-end-line="41" selection-end-column="10" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/base/VueTable.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1097">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/base/BaseCharts.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-183">
          <caret line="35" column="18" lean-forward="false" selection-start-line="35" selection-start-column="12" selection-end-line="35" selection-end-column="18" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/base/BaseForm.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1009">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/base/BaseTable.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1788">
          <caret line="19" column="46" lean-forward="false" selection-start-line="19" selection-start-column="41" selection-end-line="19" selection-end-column="46" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="454">
          <caret line="48" column="9" lean-forward="false" selection-start-line="48" selection-start-column="4" selection-end-line="48" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/App.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="38">
          <caret line="2" column="15" lean-forward="false" selection-start-line="2" selection-start-column="9" selection-end-line="2" selection-end-column="15" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/static/css/base.less">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="549">
          <caret line="45" column="62" lean-forward="false" selection-start-line="45" selection-start-column="62" selection-end-line="45" selection-end-column="62" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/common/Header.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="379">
          <caret line="58" column="19" lean-forward="true" selection-start-line="44" selection-start-column="16" selection-end-line="58" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.gitignore">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="76">
          <caret line="4" column="0" lean-forward="false" selection-start-line="4" selection-start-column="0" selection-end-line="4" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Customer-reply.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Authority-edit.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="403">
          <caret line="71" column="8" lean-forward="false" selection-start-line="71" selection-start-column="8" selection-end-line="75" selection-end-column="10" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Material-edit.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="57">
          <caret line="3" column="88" lean-forward="false" selection-start-line="3" selection-start-column="88" selection-end-line="3" selection-end-column="88" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Material.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="418">
          <caret line="22" column="20" lean-forward="false" selection-start-line="22" selection-start-column="20" selection-end-line="24" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Employee-edit.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="38">
          <caret line="2" column="34" lean-forward="false" selection-start-line="2" selection-start-column="34" selection-end-line="2" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Employee.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="399">
          <caret line="21" column="67" lean-forward="false" selection-start-line="21" selection-start-column="67" selection-end-line="21" selection-end-column="67" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Message.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="76">
          <caret line="4" column="46" lean-forward="false" selection-start-line="4" selection-start-column="42" selection-end-line="4" selection-end-column="46" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Customer.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="135">
          <caret line="8" column="62" lean-forward="false" selection-start-line="8" selection-start-column="62" selection-end-line="8" selection-end-column="62" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Message-new.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="10" lean-forward="true" selection-start-line="0" selection-start-column="10" selection-end-line="0" selection-end-column="10" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/examine/Material.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/common/Sidebar.vue">
      <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
        <state relative-caret-position="1995">
=======
        <state relative-caret-position="0">
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
          <caret line="105" column="56" lean-forward="false" selection-start-line="105" selection-start-column="48" selection-end-line="105" selection-end-column="56" />
          <folding>
            <element signature="e#11196#13125#0" expanded="false" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/setting/Authority.vue">
      <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
        <state relative-caret-position="1216">
=======
        <state relative-caret-position="0">
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
          <caret line="64" column="86" lean-forward="false" selection-start-line="64" selection-start-column="20" selection-end-line="64" selection-end-column="86" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/tip">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="2" column="66" lean-forward="false" selection-start-line="2" selection-start-column="66" selection-end-line="2" selection-end-column="66" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/router/index.js">
      <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
        <state relative-caret-position="2071">
          <caret line="110" column="0" lean-forward="true" selection-start-line="110" selection-start-column="0" selection-end-line="110" selection-end-column="0" />
=======
        <state relative-caret-position="0">
          <caret line="110" column="0" lean-forward="false" selection-start-line="110" selection-start-column="0" selection-end-line="110" selection-end-column="0" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
          <folding>
            <element signature="e#0#22#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/examine/Transport.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="11" column="30" lean-forward="false" selection-start-line="11" selection-start-column="30" selection-end-line="11" selection-end-column="30" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/public/Welcome.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="15" column="0" lean-forward="false" selection-start-line="15" selection-start-column="0" selection-end-line="15" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/common/Loading.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="17" column="10" lean-forward="false" selection-start-line="17" selection-start-column="10" selection-end-line="17" selection-end-column="10" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/page/examine/Customer.vue">
      <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
        <state relative-caret-position="57">
=======
        <state relative-caret-position="0">
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
          <caret line="3" column="62" lean-forward="false" selection-start-line="3" selection-start-column="62" selection-end-line="3" selection-end-column="62" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/common/Home.vue">
      <provider selected="true" editor-type-id="text-editor">
<<<<<<< HEAD
        <state relative-caret-position="36">
          <caret line="178" column="20" lean-forward="false" selection-start-line="178" selection-start-column="20" selection-end-line="178" selection-end-column="20" />
=======
        <state relative-caret-position="220">
          <caret line="34" column="20" lean-forward="false" selection-start-line="34" selection-start-column="20" selection-end-line="38" selection-end-column="15" />
          <folding>
            <element signature="e#1201#1234#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="252">
          <caret line="62" column="4" lean-forward="false" selection-start-line="62" selection-start-column="4" selection-end-line="62" selection-end-column="4" />
>>>>>>> 42859a55c31a7019d5c670e8a93d4355a9bc0daf
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>