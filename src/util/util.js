let hkl = {};
// 计算时间差
hkl.timeDiffer = (time1,time2)=>{
    if(time1&&time2){
        let t1 = Date.parse(new Date(time1.replace(/-/g, "/")));
        let t2 = Date.parse(new Date(time2.replace(/-/g, "/")));
        let day = parseInt((t1-t2)/86400000)
        if(day<1){
            day='<1'
        }else if(day<0){
            day=0
        }
        return day
    }else{
        return 0
    }
}
// 拷贝对象
hkl.extend = (parent)=>{
    let child={};
    for(let i in parent){
        child[i]=parent[i];
    }
    return child;
}
// 合并对象
hkl.merge=function(o,n){
    for (let i in n){
        if(n.hasOwnProperty(i))
            o[i]=n[i];
    }
};
hkl.deleteAttr=function(o,...attrs){
    for (let attr of attrs) {
        delete o[attr];
    }
};
hkl.downloadFile=function(url){
    if(url==''||url==null||url==undefined)return
    if(url instanceof Array){
        for (let i=0;i<url.length;i++) {
            try {
                download(encodeURI(url[i]));
            }catch (e) {
                window.location.href=url[i]
            }

        }
    }else{

        try {
            download(encodeURI(url));
        }catch (e) {
            window.location.href=url
        }
    }
};
// 返回数字
hkl.num = function(val){
    return val.replace(/[^\d.]/g,'').replace(/^\./g,'').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
}
export default hkl
