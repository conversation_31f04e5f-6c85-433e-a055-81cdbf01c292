/**
 * http配置
 */
// 引入axios以及element ui中的loading和message组件
import axios from 'axios'
import { Loading, Message } from 'element-ui'
// http请求拦截器
let loadinginstace,loading
let axios_instance = axios.create({
    headers:{'Content-Type':'application/json'}
});

// axios_instance.defaults.headers.common['token'] = token
axios_instance.interceptors.request.use(config => {
    // element ui Loading方法
    config.url = '/logManager'+config.url;
    loading = config.headers.closeLoading;
    delete config.headers.closeLoading;
    if(!loading){
        loadinginstace = Loading.service({ fullscreen: true,spinner: 'el-icon-loading',text:'拼命加载中',background: 'rgba(0, 0, 0, 0.5)'});
    }
    let token = localStorage.getItem('adminToken');
    if (token) {
        config.headers.common['token'] = token;
    }
    return config
}, err => {
    loadinginstace.close();
    Message.error({
        message: '请求超时!'
    })
    return Promise.reject(err)
})
// http响应拦截器
axios_instance.interceptors.response.use(response => {// 响应成功关闭loading
    const data = response.data;
    loadinginstace.close();
    if(data.code==401){
        Message.error({
            message: '请登录后再操作'
        });
        window.location.href = "/superadmin/#/login";
        localStorage.setItem('adminToken','');
        return;
    }else if(data.code!=0){
        Message.error({
            message: data.msg
        });
        return;
    }
    return data;

}, err => {
    loadinginstace.close();
    if (err && err.response) {
        switch (err.response.status) {
            case 400:
                err.message = '请求错误'
                break

            case 401:
                err.message = '未授权，请登录'
                break

            case 403:
                err.message = '拒绝访问'
                break

            case 404:
                err.message = `请求地址出错: ${err.response.config.url}`
                break

            case 408:
                err.message = '请求超时'
                break

            case 500:
                err.message = '服务器内部错误,请联系管理员'
                break

            case 501:
                err.message = '服务未实现'
                break

            case 502:
                err.message = '网关错误'
                break

            case 503:
                err.message = '服务不可用'
                break

            case 504:
                err.message = '网关超时'
                break

            case 505:
                err.message = 'HTTP版本不受支持'
                break

            default:
        }
    }
    Message.error({
        message: err.message
    })
    return Promise.reject(err)
})

export default axios_instance
