const status = {
    coStatus:[
        {value:'',label:'请选择'},
        {value:'0',label:'已到期'},
        {value:'1',label:'启用中'},
        {value:'2',label:'已冻结'}
    ],
    productStuats:[
        {value:'',label:'请选择'},
        {value:'0',label:'已停用'},
        {value:'1',label:'已启用'}
    ],
    userStatus:[
        {value:'',label:'请选择'},
        {value:0,label:'禁用'},
        {value:1,label:'正常'}
    ],
    sendType:[
        {value:'',label:'请选择'},
        {value:1,label:'直发'},
        {value:2,label:'转运'}
    ],
    baoQingGuan:[
        {value:'',label:'请选择'},
        {value:0,label:'不包清关'},
        {value:1,label:'包清关'}
    ],
    productType:[
        {value:'',label:'请选择'},
        {value:1,label:'普货'},
        {value:2,label:'特货'}
    ],
    orderStatus:[
        {value:'',label:'请选择'},
        {value:100,label:'待发货'},
        {value:200,label:'待收货'},
        {value:300,label:'待回录'},
        {value:400,label:'已收货'},
        {value:500,label:'已退回'},
        {value:600,label:'问题件'},
        {value:1000,label:'已删除'},
        {value:2000,label:'已完成'}
    ],
    rollback:[
        {value:'',label:'请选择'},
        {value:200,label:'全部退回'},
        {value:210,label:'部分退回'}
    ],
    question:[
        {value:'',label:'请选择'},
        {value:500,label:'收货少货'},
        {value:510,label:'转运滞留'},
        {value:520,label:'海关扣留'},
        {value:530,label:'找不到收件人'},
    ],
    messageType:[
        {value:'',label:'请选择'},
        {value:1,label:'全部'},
        {value:2,label:'快递公司'},
        {value:3,label:'商户'}
    ],
}
const filters = {
    formatters:function(val, format) {
        if (typeof (format) === 'function') {
            return format(val)
        } else return val
    },
    coStatus:function(val) {
        let str='',len = status.coStatus.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.coStatus[i].value === val){
                    str = status.coStatus[i].label;
                    break;
                }
            }
        }
        return str
    },
    productStuats:function(val) {
        let str='',len = status.productStuats.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.productStuats[i].value === val){
                    str = status.productStuats[i].label;
                    break;
                }
            }
        }
        return str
    },
    userStatus:function(val) {
        let str='',len = status.userStatus.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.userStatus[i].value === val){
                    str = filterLabel(status.userStatus[i].label);
                    break;
                }
            }
        }
        return str
    },
    sendType:function(val) {
        let str='',len = status.sendType.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.sendType[i].value === val){
                    str = status.sendType[i].label;
                    break;
                }
            }
        }
        return str
    },
    baoQingGuan:function(val) {
        let str='',len = status.baoQingGuan.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.baoQingGuan[i].value === val){
                    str = status.baoQingGuan[i].label;
                    break;
                }
            }
        }
        return str
    },
    productType:function(val) {
        let str='',len = status.productType.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.productType[i].value === val){
                    str = status.productType[i].label;
                    break;
                }
            }
        }
        return str
    },
    orderStatus:function(val) {
        let str='',len = status.orderStatus.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.orderStatus[i].value === val){
                    str = status.orderStatus[i].label;
                    break;
                }
            }
        }
        return str
    },
    rollback:function(val) {
        let str='',len = status.rollback.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.rollback[i].value === val){
                    str = status.rollback[i].label;
                    break;
                }
            }
        }
        return str
    },
    question:function(val) {
        let str='',len = status.question.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.question[i].value === val){
                    str = status.question[i].label;
                    break;
                }
            }
        }
        return str
    },
    messageType:function(val) {
        let str='',len = status.messageType.length;
        if(val!=undefined){
            for(let i=0;i<len;i++){
                if(status.messageType[i].value === val){
                    str = status.messageType[i].label;
                    break;
                }
            }
        }
        return str
    },
    filterLabel:function(val) {
        if(!val)return
        let str = val.indexOf('-')>-1?val.split('-')[1]:val;
        return str
    }
}
const filterLabel = function(val) {
    if(!val)return
    let str = val.indexOf('-')>-1?val.split('-')[1]:val;
    return str
}
export{
    status,
    filters
}

