//初始化设置
$(function(){
    $(".fa-angle-double-right").parents("tr").nextAll().hide();

    //随附单据提示
    $("[data-toggle='tooltip']").tooltip();
});
$("#mTips").bind("mouseover", function() {
    if(!$("#decDocBtn").prop("disabled")){
        $("#btnCover").css("display","none");
    }else{
        $("#btnCover").css("display","block");
    }
});
/**
 * 特殊关系确认
 */
function getPromiseItems(){
    var promiseItem1 = $("#promiseItem1").val();
    if(!promiseItem1){
        promiseItem1="9";
    }
    var promiseItem2 = $("#promiseItem2").val();
    if(!promiseItem2){
        promiseItem2="9";
    }
    var promiseItem3 = $("#promiseItem3").val();
    if(!promiseItem3){
        promiseItem3="9";
    }
    return promiseItem1+promiseItem2+promiseItem3;
}

/**
 * 特殊关系确认
 * @param promiseItems
 * @returns
 */
function setPromiseItems(promiseItems){
    var promiseItem = $("*[id^=promiseItem]") ;
    if(promiseItems!=null){
        var arr = promiseItems.split('');
        for (var i = 1; i < arr.length+1; i++) {
            $("#promiseItem"+i).val(arr[i-1]);'promise'+i+'Name',
                $('#promise'+i+'Name').autocomplocal({// 显示框
                    tableName:'DEC_SPECL_RL_AFM_CODE',// 查询的同义词
                    hiddenId:'promiseItem'+i,// 隐藏框
                    keyValue:arr[i-1]//默认显示
                });
        }
    }
}
//其他事项确认
$("#btn-promiseItem").click(function(){
    openWindown('其他事项确认','promise1Name','#div-promiseItem',['确定'],['650px','270px'],promiseItemCallBack,promiseItemCallBack);
});
function promiseItemCallBack(){
    layer.closeAll();
    var cusTrafMode=$("#dec_head_form input[id=cusTrafMode]").val();
    if("5"==cusTrafMode){
        $("#dec_license_form input[id=acmpFormCodeName]").focus();
    }else{
        $("#dec_head_div").removeClass("foucsStyle");
        $("#dec_container_div").addClass("foucsStyle");
        $("#dec_Container_form input[id=containerNo]").focus();
    }
}
//业务选项
$("#btn-cusRemark").click(function(){
    var supvModeCdde=$("#supvModeCdde").val();//运输方式
    var cusRmftFcbFlag = $("#cusRmftFcbFlag").val();//公自用物品申报flag
    layer.open({
        title:'业务事项',
        type: 1,
        btn:'确定',
        content: $('#div-cusRemark'), //这里content是一个普通的String
        area:['505px','260px'],
        btnAlign : 'c',// 居中
        success: function(layero, index){
            $(document).on('keydown', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                if(e.keyCode == 13){
                    return false;
                }
            });
            $(".div-cusRemark-css-item").find("input").removeAttr("disabled");

            if(dclTrnRelFlag){//清单类型
                switch (parseInt(dclTrnRelFlag)) {
                    case 0://一般报关单
                        $("#cusRemark1").focus();
                        break;
                    case 1://转关报关单
                        $(".div-cusRemark-css-item").hide();
                        $("#cusRemark1").parent().show();
                        $("#cusRemark6").parent().show();
                        break;
                    case 2://备案清单
                        $(".div-cusRemark-css-item").hide();
                        if(cusRmftFcbFlag != "2"){
                            $("#cusRemark1").parent().show();
                            $("#cusRemark6").parent().show();
                            if(cusIEFlag == "E"){
                                $("#cusRemark4Div").show();//水运中转
                            }
                        }else{
                            $("#cusRemark6").parent().show();
                        }
                        break;
                    case 3://转关提前备案清单
                        $(".div-cusRemark-css-item").hide();
                        /*						$("#cusRemark1").parent().show();*/
                        $("#cusRemark6").parent().show();
                        break;
                    default:
                        break;
                }
            }
            if(dclTrnRelFlag && cusRmftFcbFlag != "null" && cusRmftFcbFlag == "2"){
                $("#cusRemark6").focus(); //公路舱单备案清单
            }else{
                if(cusRmftFcbFlag == "1"){
                    $(".div-cusRemark-css-item").hide();
                    $("#cusRemark1").parent().show();
                    $("#cusRemark6").parent().show();
                }
                if(document.getElementById("cusRemark4Div")){
                    var cusRemark4DivDisplay=$('#cusRemark4Div').css('display');
                    if("none"==cusRemark4DivDisplay){
                        $("#cusRemark1").focus();
                    }else{
                        $("#cusRemark4").focus();
                    }
                }else{
                    $("#cusRemark1").focus();
                }
            }
            if(isCusStatus()){
                $("#div-cusRemark").find("input").attr("disabled",true);
            }else{
                cusRemarkStatusControl();//状态控制
                var cusRemark1Checked=$("#cusRemark1").is(':checked');//自报自缴
                var cusRemark4Checked=$("#cusRemark4").is(':checked');//水运中转
                var independentTaxChecked=$("#independentTax").is(':checked');//自主报税
                if(cusRemark1Checked){
                    $("#independentTax").attr("disabled",true);
                    if(dclTrnRelFlag ==2 && cusRmftFcbFlag != "2"){//备案清单
                        $("#cusRemark4").attr("disabled",true);
                    }
                }
                if(independentTaxChecked){//自主报税选中了
                    $("#cusRemark1").attr("disabled",true);
                }
                if(cusRemark4Checked){//水运中转选中了
                    if(dclTrnRelFlag ==2 && cusRmftFcbFlag != "2"){//备案清单
                        $("#cusRemark1").attr("disabled",true);
                    }
                }
            }

            var inputCheckFlag=false;//是否所有显示的复选框都没有否获取焦点
            $(".div-cusRemark-css-item").each(function(i,item){
                if($(item).css("display") != "none"){
                    var tempFocusId=$(item).find("input").attr("id");
                    if(tempFocusId){
                        var input = document.getElementById(tempFocusId);
                        if(document.hasFocus()){
                            inputCheckFlag=true;
                            return false;
                        }
                    }
                }
            });
            if(!inputCheckFlag){
                $(document).keydown(function(event){
                    if(event.keyCode == 32){//阻止事件传递
                        event.preventDefault();
                        return false;
                    }
                });
            }
        },
        calcel:function(index,layero){
            layer.close(index);
            cusRemarkSkip();
        },
        yes:function(index,layero){
            layer.close(index);
            cusRemarkSkip();
        },
        end:function(){
            if("4"==cusRmftFcbFlag){
                //判断公自用物品按钮是否可用
                changePublicSelfUse();
            }
        }
    });
});
function bussinessTypeChange(id){
    var cusRemark=$("#cusRemark").val();
    var b_cusRemark=$("#"+id).is(':checked');
    if(b_cusRemark){
        var i=id.substr(-1);
        if(cusRemark){
            var length=cusRemark.length;
            if(length>=i){
                cusRemark=cusRemark.substr(0,i-1)+"1"+cusRemark.substr(i,length);
            }else{
                for(var j=1;j<i-length;j++){
                    cusRemark=cusRemark+"0";
                }
                cusRemark=cusRemark+"1";
            }
        }else{
            for(var j=0;j<i-1;j++){
                cusRemark=cusRemark+"0";
            }
            cusRemark=cusRemark+"1";
        }


        if(id == "cusRemark4"){
            $("#cusRemark1").attr("disabled",true);
        }
        if(id == "cusRemark1"){
            if(dclTrnRelFlag ==2 && cusRmftFcbFlag != "2"){//备案清单
                $("#cusRemark4").attr("disabled",true);
            }
            $("#independentTax").attr("disabled",true);
        }
    }else{
        var i=id.substr(-1);
        if(cusRemark){
            var length=cusRemark.length;
            if(length>=i){
                cusRemark=cusRemark.substr(0,i-1)+"0"+cusRemark.substr(i,length);
            }else{
                for(var j=1;j<i-length;j++){
                    cusRemark=cusRemark+"0";
                }
                cusRemark=cusRemark+"0";
            }
        }else{
            for(var j=0;j<i-1;j++){
                cusRemark=cusRemark+"0";
            }
            cusRemark=cusRemark+"0";
        }

        if(id == "cusRemark4"){
            cusRemarkStatusControl();
        }
        if(id == "cusRemark1"){
            var billType=$("#billType").val();
            if(billType && cusRmftFcbFlag != "2"){//备案清单
                if(billType == "1" || billType == "4"){
                    $("#cusRemark4").removeAttr("disabled");
                }
            }else{
                $("#cusRemark4").removeAttr("disabled");
            }
            $("#independentTax").removeAttr("disabled");
        }
    }
    $("#cusRemark").val(cusRemark);
}


/**
 * 特殊关系确认
 */
function speclRlAfmCodeChange(){
    var promiseItem1=$("#promiseItem1").val();
    if("0"==promiseItem1){
        $("#promiseItem2").val("0");
        $("#promise2Name").val("否");
        $("#promise2Name").attr("disabled", true);
        $("#promise2Btn").attr("disabled", true);
    }else{
        $("#promise2Name").attr("disabled", false);
        $("#promise2Btn").attr("disabled", false);
    }
}


/**
 * 特殊关系确认
 * @param promiseItems
 * @returns
 */
function setPromiseItems(){
    var promiseItem = $("*[id^=promiseItem]") ;
    var promiseItems = $("#promiseItems").val();
    if(promiseItems!=null){
        var arr = promiseItems.split('');
        for (var i = 1; i < arr.length+1; i++) {
            $("#promiseItem"+i).val(arr[i-1]);'promise'+i+'Name',
                $('#promise'+i+'Name').autocomplocal({// 显示框
                    tableName:'DEC_SPECL_RL_AFM_CODE',// 查询的同义词
                    hiddenId:'promiseItem'+i,// 隐藏框
                    keyValue:arr[i-1]//默认显示
                });
        }
    }
}

$("#showSpecDeclFlagBtn").bind('click',function(){
    openWindown("特殊业务标识",'specDeclFlag1','#specDeclFlagDiv',['确定'],[ '500px', '250px' ],enterToGoodsList,specDeclFlagConfirmBack,openSpecDeclFlagSuccess);
});
function specDeclFlagConfirmBack(){
    specDeclFlagOperate();
    enterToGoodsList();
}
//特殊业务标识
function specDeclFlagCheck(){
    var specDeclFlag = $("#specDeclFlag").val();
    if(specDeclFlag!=null && specDeclFlag!=""){
        var specDeclFlags = specDeclFlag.split('');
        for (var i=0; i<specDeclFlags.length; i++) {
            if (specDeclFlags[i] == '1') {
                $("#specDeclFlag" + (i+1)).prop('checked', true);
            }
        }
    }
    var specPassFlag = $("#specPassFlag").val();
    if(specPassFlag!=null && specPassFlag!=""){
        var specPassFlags=specPassFlag.split('');
        for (var i=0; i<specPassFlags.length; i++) {
            if (specPassFlags[i] == '1') {
                $("#specPassFlag" + (i+1)).prop('checked', true);
            }
        }
    }
    specDeclFlagOperate();
}
//特殊业务标识打开弹框回调
function openSpecDeclFlagSuccess(){
    $("#specDeclFlagDiv").find(".div-cusRemark-css-item").show();
    if("E" == cusIEFlag){
        $("#specPassFlag4").parent(".div-cusRemark-css-item").hide();
    }
    if(isCusStatus()){
        $("#specDeclFlagDiv").find("input").attr("disabled",true);
    }else{
        $("#specDeclFlagDiv").find("input").removeAttr("disabled");
        $("#specDeclFlag1").focus();
    }
}
//特殊业务标识
function specDeclFlagOperate(){
    var sels=$("#specDeclFlagDiv input[type='checkbox']:checked");
    //特种业务标识
    var specDeclFlagValue = '';
    //特殊通关模式
    var specPassFlagValue = '';
    var content='';
    if(sels.length>0){
        for(var i=0;i< sels.length;i++){
            content+=$(sels[i]).attr("showName")+",";
        }
        content=content.substring(0,content.length-1);
    }
    $("#specDeclFlagInput").val(content);
}


/**
 * 特种业务标识拼接处理。
 */
function getSpecDeclFlag() {
    //特种业务标识
    var specDeclFlagValue = '';
    var specDeclFlags = $("#specDeclFlagDiv input[id^=specDeclFlag] ") ;
    if(specDeclFlags!=null){
        for (var i = 0; i < specDeclFlags.length; i++) {
            if(specDeclFlags[i].checked){
                specDeclFlagValue += '1';
            }else{
                specDeclFlagValue += '0';
            }
        }
    }
    return specDeclFlagValue;
}

/**
 * 特殊通关模式拼接处理。
 */
function getSpecPassFlag() {
    //特殊通关模式
    var specPassFlagValue = '';
    var specPassFlags = $("#specDeclFlagDiv input[id^=specPassFlag] ") ;
    if(specPassFlags!=null){
        for (var i = 0; i < specPassFlags.length; i++) {
            if(specPassFlags[i].checked){
                specPassFlagValue += '1';
            }else{
                specPassFlagValue += '0';
            }
        }
    }
    return specPassFlagValue;
}

//标记唛码
$("#showMarkNo").click(function(){
    openWindown("标记唛码",'markNoSLa',"#markNoSLayer",[],[ '450px', '305px' ],null,null,successMarkNoBack,endMarkNoBack);
});
//备注
$("#showNoteS").click(function(){
    openWindown("备注",'noteSLa',"#noteSLayer",[],[ '450px', '305px' ],null,null,successNoteBack,endNoteBack);
});
function successNoteBack(){
    value=$("#noteS").val();
    $("#noteSLa").focus();//默认光标定位
    $("#noteSLa").val(value);
    document.getElementById('noteSByteTotalLa').innerHTML =decGetlen(value,2);
}
function endNoteBack(){
    value=$("#noteSLa").val();
    $("#noteS").val(value);
    document.getElementById('noteSByteTotal').innerHTML =decGetlen(value,2);
}
function successMarkNoBack(){
    value=$("#markNo").val();
    $("#markNoSLa").focus();//默认光标定位
    $("#markNoSLa").val(value);
    document.getElementById('markNoSByteTotalLa').innerHTML =decGetlen(value,2);
}
function endMarkNoBack(){
    value =$("#markNoSLa").val();
    $("#markNo").val(value);
    document.getElementById('markNoByteTotal').innerHTML =decGetlen(value,2);
}
/**
 * 获取字符串长度
 */
function decGetlen(value,defaultFlag) {
    if(!defaultFlag){
        defaultFlag = 2;
    }else{
        defaultFlag = Number(defaultFlag);
    }
    var realLength = 0;
    if(!value){
        return 0;
    }
    var str = value;
    var len = str.length;
    //utf-8字节长度
    for (var i = 0; i < len; i++) {
        charCode = str.charCodeAt(i);
        if (charCode >= 0 && charCode <= 128) {
            realLength += 1;
        } else {
            // 如果是中文则长度加2
            realLength += defaultFlag;
        }
    }
    return realLength;
}
/*备注监听*/
listeningOnNote('dec_head_form','noteS','noteSByteTotal');
listeningOnNote('dec_head_form','markNo','markNoByteTotal');
listeningOnNote('noteSLayer','noteSLa','noteSByteTotalLa');
listeningOnNote('markNoSLayer','markNoSLa','markNoSByteTotalLa');
//附注信息监听
listeningOnNote('addtionLayer','additionInfoLa','addtionByteTotalLa');

function listeningOnNote(divId,inputId,totalId){
    $("#"+inputId).bind('input propertychange', function() {
        var value = $(this).val();
        if(document.getElementById(totalId)){
            var totalValue=decGetlen(value,2);
            var maxLen=(inputId == "markNo" || inputId == "markNoSLa")?400:255;
            if(inputId == "additionInfoLa"){
                maxLen=2000;
            }
            document.getElementById(totalId).innerHTML = totalValue > maxLen ? maxLen:totalValue;
        }
    });
}
function trToggleAll(obj,event){
    $("#decHeadShow").click();
    $("#decListShow").click();
}
//收缩切换
function trToggle(obj){
    var iTag=$(obj).find('i');
    if(iTag.hasClass("fa-angle-double-down")){
        $(obj).attr("isDecListShow","0")
        iTag.removeClass("fa-angle-double-down");
        iTag.addClass("fa-angle-double-right");
        $(obj).parent().parent().nextAll().hide();
    }else{
        $(obj).attr("isDecListShow","1")
        iTag.addClass("fa-angle-double-down");
        iTag.removeClass("fa-angle-double-right");
        $(obj).parent().parent().nextAll().show();
        if($(obj).attr("id") == "decListShow"){//页面置底
            document.getElementsByTagName('BODY')[0].scrollTop=document.getElementsByTagName('BODY')[0].scrollHeight;
        }
    }
}
//上传随附单据文件
$("#uploadDocBtn").click(function(){
    // 暂时与标记唛码上传文件共用同一上传弹框
    layer.open({
        type:1,
        shadeClose:false,
        area:['500px','220px'],
        title:"上传随附单据文件",
        shade:0.5,
        scrollbar:false,
        btn:[],//按钮名称
        btnAlign: 'c',		//居中
        success: function(index, layero){
            $(document).on('keydown', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                if(e.keyCode == 13){
                    return false;
                }
            })
        },
        yes:function(index){
            layer.close(index);
            $("#inspGoodsOptionsAddBtn").attr("disabled", false);
        },
        cancel:function(index){
            layer.close(index);
            $("#inspGoodsOptionsAddBtn").attr("disabled", false);
        },
        content:$("#inspGoodsOptionsAddContent")
    })
});
//标记唛码及上传附件
$("#inspGoodsOptionsAddBtn").click(function(){
    $("#inspGoodsOptionsAddBtn").attr("disabled", true);
    var data ={cusCiqNo:$("#cusCiqNo").val(),cusIEFlag:cusIEFlag,limit : 500,// 页面大小
        offset : 0}
    $.ajax({
        type : "POST",
        url : swProxyBasePath+"sw/dec/ciq/queryBCiqDecLicense",
        data : JSON.stringify(data),
        dataType : "json",
        contentType : "application/json; charset=utf-8",
        success : function(data) {
            getCiqDocTable(data.rows);
        }
    });
    /**
     * 标记及号码上传附件
     */
    layer.open({
        type:1,
        shadeClose:false,
        area:['500px','220px'],
        title:"编辑标记及号码附件信息",
        shade:0.5,
        scrollbar:false,
        btn:[],//按钮名称
        btnAlign: 'c',		//居中
        success: function(index, layero){
            $(document).on('keydown', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                if(e.keyCode == 13){
                    return false;
                }
            })
        },
        yes:function(index){
            layer.close(index);
            $("#inspGoodsOptionsAddBtn").attr("disabled", false);
        },
        cancel:function(index){
            layer.close(index);
            $("#inspGoodsOptionsAddBtn").attr("disabled", false);
        },
        content:$("#inspGoodsOptionsAddContent")
    })
});
//货物属性
function showBussinessPosDiv(id,divId,foucsId){
    //打开界面初始化
    var obj = document.getElementsByTagName("td");
    //清空颜色
    for (var i = 0; i < obj.length; i++) {
        if (obj[i].className == 'bgcolor') {
            obj[i].className='';
        }
    }
    var label = $("#goodsAttr").val();
    var arr = label.split(",");
    for(var i=0;i< obj.length;i++){
        for(var j=0;j< arr.length;j++){
            if(obj[i].getAttribute("id") == arr[j]){
                $(obj[i]).addClass('bgcolor');
            }
        }
    }
    layer.open({
        type : 1,
        shadeClose : true,
        area : [ '650px', '320px' ],
        title : "货物属性",
        shade : 0.5,
        scrollbar : false,
        btn : [ '确定', '取消' ],
        btnAlign : 'c',
        success: function(index, layero){
            $("#"+id).blur();
            $(document).on('keydown', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                if(event.shiftKey!=1&&e.keyCode == 13){
                    return false;
                }
            });
            $(index.context).on('keyup', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                if(event.shiftKey!=1&& e.keyCode == 13){
                    setGoodsAttrName(divId,foucsId);
                }
            });
            $("#"+divId).on('keyup', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                if( e.keyCode == 13 && !(e.target.id == 'prodBatchNo' || e.target.id == 'mnufctrRegNo')){
                    setGoodsAttrName(divId,foucsId);
                }
            });
        },
        yes : function(index) {
            setGoodsAttrName(divId,foucsId);
        },
        content : $("#bussinessPosDiv")
    })
}
/*设置属性名称*/
function setGoodsAttrName(divId,foucsId){
    //判断货物属性是否弹框
    if(!foucsId){
        foucsId = "goodsSpec";
    }
    if($("#bussinessPosDiv")[0].style.display=="none"){
        return false;
    }
    var obj = document.getElementsByTagName("td");
    var goodsAttrName = "";
    var goodsAttr="";
    var i = 0,j=0;
    for (i; i < obj.length; i++) {
        if (obj[i].className == 'bgcolor') {
            if(goodsAttrName==""){
                goodsAttrName = obj[i].getAttribute("name");
                goodsAttr=obj[i].getAttribute("id");
            }else{
                goodsAttrName = goodsAttrName + ',' + obj[i].getAttribute("name");
                goodsAttr= goodsAttr + ',' + obj[i].getAttribute("id");
            }
            if(goodsAttr.length>20){
                layer.tips("货物属性选择项过多，最多只能选择"+j+"项。",  //所需提醒的信息
                    $("#goodsAttrName"),{		//所需提醒的元素
                        tips: [1,'#DC143C'], //在元素的下面出现 1上面，2右边 3下面
                        tipsMore: false, //允许同时存在多个
                        time:2000 //tips自动关闭时间，毫秒
                    });
                return;
            }
            j++;
        }
    }
    $("#goodsAttr").val(goodsAttr);
    $("#goodsAttrName").val(goodsAttrName);
    layer.closeAll();
    $("#"+divId+" input[id="+foucsId+"]").focus();
}

$("#goodsTargetBtn").click(function(){
    //货物指标
    openWindown("编辑检验检疫货物规格",'stuff','#inspGoodsTargetDiv',['确定'],['540px','380px'],goodsTargetCloseCallBack,goodsTargetBtnCallBack,null,null);
    var stuffReadonly = $("#dec_goods_list_form input[id='stuff']").attr("readonly");
    if(stuffReadonly){
        $("#stuff").blur();//移除关灯效果
    }
})
function goodsTargetCloseCallBack(){
    $("#purposeName").focus();
}
function goodsTargetBtnCallBack(){
    goodsTargetFull();
    $("#purposeName").focus();
}

/**
 * 根据格式11-11-11111111111111填充许可证号
 * @param id
 * @param e
 */
function fill(id, e,flag) {
    //13：Enter(回车)，8：Backspace(删除)
    var eCode = e.keyCode ? e.keyCode : e.which ? e.which : e.charCode;
    var licenseNo = $("#" + id).val();
    var length = licenseNo.length;
    if (eCode != 8 && (length == 2 || length == 3 || length == 5 || length == 6)) {
        var last = licenseNo.substring(length - 1);
        if (last != "-") {
            if (length == 3 || length == 6) {
                licenseNo = licenseNo.substring(0, (length - 1)) + '-' + last;
            } else {
                licenseNo += '-';
            }
            $("#" + id).val(licenseNo);
        }
    } else if (eCode == 8) {//删除
        if (length > 0) {
            var last = licenseNo.substring(length - 1);
            if (last == "-") {
                $("#" + id).val(licenseNo.substring(0, (length - 1)));
            }
        }
    } else if (e.shiftKey!=1&&eCode == 13) {//回车
        $("#dec_head_form input[id='cusTradeCountryName']").focus();
    }
}
//反填内容
function goodsTargetFull(data){
    if(!data){
        if(isCusStatus()){
            return;
        }
        data=$("#dec_goods_list_form").serializeObject();
    }
    var goodsTargetContent="";
    if(data['stuff']) goodsTargetContent+=";"+data['stuff'];
    if(data['prodValidDt']) goodsTargetContent+=";"+data['prodValidDt'];
    if(data['prodQgp']) goodsTargetContent+=";"+data['prodQgp'];
    if(data['engManEntCnm']) goodsTargetContent+=";"+data['engManEntCnm'];
    if(data['goodsSpec']) goodsTargetContent+=";"+data['goodsSpec'];
    if(data['goodsModel']) goodsTargetContent+=";"+data['goodsModel'];
    if(data['goodsBrand']) goodsTargetContent+=";"+data['goodsBrand'];
    if(data['produceDate']) goodsTargetContent+=";"+data['produceDate'];
    if(data['prodBatchNo']) goodsTargetContent+=";"+data['prodBatchNo'];
    if(data['mnufctrRegNo']) goodsTargetContent+=";"+data['mnufctrRegNo'];
    $("#goodsTargetBtn").prev().val(goodsTargetContent.substring(1,goodsTargetContent.length));
}



//表头回车事件到商品表体
function changeFoucsToGName(event,flag) {}

//52开头的申报地海关货场代码反填
function deCustomsFieldEnterSave(event,formId){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if (event.shiftKey!=1&&eCode == 13) {
        getCustomField(formId);
    }
}

/**
 * 货场代码name为空，则货场代码code也清空
 */
$("#customsFieldName").blur(function() {
    if(!$.trim($('#customsFieldName').val())){
        $('#customsField').val('');
    }else {
        $('#customsField').val($.trim($('#customsFieldName').val()));
    }
})

function getCustomField(formId){
    var customsFieldName= $("#"+formId+" input[id='customsFieldName']").val();
    var customMaster = $("#"+formId+" input[id='customMaster']").val();
    if(customMaster && customsFieldName && customMaster.length>=2 && customMaster.substring(0,2)=="52"){
//		var sql="select * from CUS_GOODS_PLACE where  ( place_code =? or place_name=?) and (master_customs=? or master_customs=?)";
        var paraList=[];
        paraList.push(customsFieldName);
        paraList.push(customsFieldName);
        paraList.push(customMaster);
        paraList.push(customMaster.substring(0,2)+"00");
        var params = {
//				"sql" : sql,
            'paraList' : paraList
        };
        $.ajax({
//	    		url : swProxyBasePath+'sw/base/para/depParaListBySql',
            url : swProxyBasePath+'sw/dec/common/depParaListBySql',
            data : JSON.stringify(params),
            async: false,
            dataType : "json",
            type : "post",
            contentType : "application/json;charset=utf-8",
            success: function (data) {
                if(data && data.length>0 && data[0].placeName!=""&&data[0].placeName!=" "&&data[0].placeName!=null){
                    $("#"+formId+" input[id='customsField']").val(data[0].placeCode);
                    $("#"+formId+" input[id='customsFieldName']").val(data[0].placeName);
                }else{
                    layerMsg("货场代码输入错误，请重新录入！", 2, 2000);
                    $("#"+formId+" input[id='customsField']").val(customsFieldName);
                    $("#"+formId+" input[id='customsFieldName']").val(customsFieldName);
                }
            }
        });
    }else{
        $("#"+formId+" input[id='customsField']").val(customsFieldName);
        $("#"+formId+" input[id='customsFieldName']").val(customsFieldName);
    }
}
//率格式控制
//率,用户录入范围是0.0001-99，代表费率是0.0001%-99%
//单价, 整数最多录入10位，小数点后面最多录入4位。
//总价,整数最多录入12位，小数点后面最多录入4位。
function rateStyle(markId,rateId){
    var markVal = $("#" + markId).val();
    var rateVal = $("#" + rateId).val();
    var reg1 = /^\d{0,10}(\.\d{0,4})?$/;
    var reg2 = /^\d{0,12}(\.\d{0,4})?$/;
    var reg3 = /^(\-?)\d{0,12}(\.\d{0,4})?$/;
    var reg4 = /^(\-?)\d{0,2}(\.\d{0,4})?$/;//费率长度限制， 整数最多录入2位，小数点后面最多录入4位。
    if (markVal == 1) {
        if(rateVal){
            if(rateId == "otherRate"){//杂费费率 0.0001到99 ,-99到-0.0001
                if(!(((rateVal >= 0.0001 && rateVal <= 99) || (rateVal >= -99 && rateVal <= -0.0001)) && reg4.test(rateVal))){
                    layer.tips("录入值范围为0.0001到99或-99到-0.0001", // 所需提醒的信息
                        $("#" + rateId), { // 所需提醒的元素
                            tips : [ 2, '#DC143C' ], // 在元素的下面出现 1上面，2右边 3下面
                            tipsMore : true, // 允许同时存在多个
                            time : 2000 // tips自动关闭时间，毫秒
                        });
                    $("#" + rateId).val("");
                }
            }else{
                if (rateVal < 0.0001 || rateVal > 99 || !reg4.test(rateVal)) {
                    layer.tips("录入值范围为0.0001-99", // 所需提醒的信息
                        $("#" + rateId), { // 所需提醒的元素
                            tips : [ 2, '#DC143C' ], // 在元素的下面出现 1上面，2右边 3下面
                            tipsMore : true, // 允许同时存在多个
                            time : 2000 // tips自动关闭时间，毫秒
                        });
                    $("#" + rateId).val("");
                }
            }
        }
    } else if (markVal == 2) {
        if (!reg1.test(rateVal)) {
            layer.tips("整数最多录入10位，小数点后面最多录入4位", // 所需提醒的信息
                $("#" + rateId), { // 所需提醒的元素
                    tips : [ 2, '#DC143C' ], // 在元素的下面出现 1上面，2右边 3下面
                    tipsMore : true, // 允许同时存在多个
                    time : 2000 // tips自动关闭时间，毫秒
                });
            $("#" + rateId).val("");
        }
    } else if (markVal == 3) {
        var flag;
        if(rateId=="otherRate"){
            flag = !reg3.test(rateVal);
        }else{
            flag = !reg2.test(rateVal);
        }
        if (flag) {
            layer.tips("整数最多录入12位，小数点后面最多录入4位", // 所需提醒的信息
                $("#" + rateId), { // 所需提醒的元素
                    tips : [ 2, '#DC143C' ], // 在元素的下面出现 1上面，2右边 3下面
                    tipsMore : true, // 允许同时存在多个
                    time : 2000 // tips自动关闭时间，毫秒
                });
            $("#" + rateId).val("");
        }
    }
}
/**
 * 备案号录入转大写
 */
$('body').delegate('#manualNo','blur',"obj",function(obj){
    this.value=this.value.toUpperCase();
})

$("#manualNo").css("text-transform","uppercase");

//标记唛码回车跳转
function markNoEnterToNext(event){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey&&eCode == 13){//回车键
        if(event.ctrlKey && event.keyCode == 13){
            return;
        }
        layer.closeAll();
        var entryType=$("#entryType").val();
        if("M" != entryType){
            enterToShowHeadOrGoodsList()
        }else{
            if(cusIEFlag=="I"){
                $("#entryTypeName").blur();
                $("#btn-cusRemark").click();
                if(dclTrnRelFlag == "3"){//转关备案清单
                    $("#cusRemark6").focus();
                }else{
                    $("#cusRemark1").focus();
                }
            }else if(cusIEFlag=="E"){
                var cusRemark4DivDisplay=$('#cusRemark4Div').css('display');
                if("none"==cusRemark4DivDisplay){
                    $("#entryTypeName").blur();
                    $("#btn-cusRemark").click();
                    $("#cusRemark1").focus();
                }else{
                    $("#btn-cusRemark").click();
                    $("#cusRemark4").focus();
                }

            }
        }

    }else if(event.shiftKey&&eCode == 13){

    }
}
//跳转表头折叠部分或商品表体部分，依情况而定
function enterToShowHeadOrGoodsList(){
    var orgCodeNameDisplay=$('#decHeadShow').parents("tr").next().css('display');
    if('none' == orgCodeNameDisplay){//检验检疫受理机关
        enterToGoodsList();
    }else{
        $("#orgCodeName").focus();
    }
}
function enterToGoodsList(){
    $("#dec_head_div").removeClass("foucsStyle");
    $("#dec_goods_div").addClass("foucsStyle");
    var contrItemReadOnly = $("#dec_goods_list_form   input[id='contrItem']").attr("readonly");
    var contrItemDisabled = $("#dec_goods_list_form   input[id='contrItem']").attr("disabled");
    if(contrItemReadOnly=="readonly" || contrItemReadOnly==true || contrItemDisabled=="disabled"  || contrItemDisabled==true){
        $("#dec_goods_list_form input[id=gName]").focus();
    }else{
        $("#dec_goods_list_form input[id=contrItem]").focus();
    }
}

/*$('#dec_goods_list_form input[id="gName"]').bind('input propertychange', function() {
	if($(this).val()){
		$(this).attr("isChange", "true");
	}
});*/


//商品名称回车到商品编号
function changeFoucsToCodeTs(e) {
    var eCode = e.keyCode ? e.keyCode : e.which ? e.which : e.charCode;
    if (eCode == 13) {
        var certCheckFlag = getDecCertCheckSwitch();
        if("1"==certCheckFlag){
            //涉检不涉检校验
            var ciqCode =$("#dec_goods_list_form input[id='ciqCode']").val();
//			var changeFlag = $("#dec_goods_list_form input[id='gName']").attr("isChange");

            var codeTs=$("#codeTs").val();
            var ciqCodeTs=$("#ciqCodeTs").val();

//			if(!ciqCode||changeFlag=="true"){
            if(!ciqCode||codeTs!=ciqCodeTs){
//				$("#dec_goods_list_form input[id='gName']").attr("isChange","false");
                var gName=$("#dec_goods_list_form input[id='gName']").val();
                var gModel=$("#dec_goods_list_form input[id='gModel']").val();
                var codeTs=$("#dec_goods_list_form input[id='codeTs']").val();
                var ciqGoodsRuleFlag=newCiqGoodsRule(codeTs,gName,gModel);
                if(ciqGoodsRuleFlag){
                    newLoadCiqCode(codeTs);
                }
            }
        }
        $("#dec_goods_list_form input[id=codeTs]").focus();
    }else if(e.ctrlKey && eCode == 35){//ctrl +end
        $("#dec_head_form input[id=relativeId]").focus();
    }
}

//表头回车事件到集装箱
function changeFoucsToDecContainer() {
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if (event.shiftKey!=1 && eCode == 13) {
        promiseItemCallBack();
    }
}
//
function changeFoucsTogoodsAttr(event){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if (event.shiftKey!=1 && eCode == 13) {
        $("#prodBatchNo").blur();
        layer.closeAll();
        goodsTargetFull();
        $("#showBussinessPosDivBtn").click();
    }
}

function cusRemark6Enter(event){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey&&eCode == 13){//回车键
        layer.closeAll();
        cusRemarkSkip();
    }else if(event.shiftKey&&eCode == 13){
        if(cusRmftFcbFlag && cusRmftFcbFlag != "null" && cusRmftFcbFlag == "2"){
            layer.close(layerCusRemark);
            $("#markNo").focus();
        }else{
            $("#cusRemark1").focus();
        }
    }
}
function cusRemarkSkip(){//业务事项跳转
    var supvModeCdde=$("#supvModeCdde").val();
    if("0255"==supvModeCdde||"0654"==supvModeCdde){
        $("#relativeId").focus();
    }else{
        var orgCodeNameDisplay=$('#decHeadShow').parents("tr").next().css('display');
        if("none" == orgCodeNameDisplay){
            $("#dec_head_div").removeClass("foucsStyle");
            $("#dec_container_div").addClass("foucsStyle");
            var contrItemReadOnly = $("#dec_goods_list_form   input[id='contrItem']").attr("readonly");
            var contrItemDisabled = $("#dec_goods_list_form   input[id='contrItem']").attr("disabled");
            if(contrItemReadOnly=="readonly" || contrItemReadOnly==true || contrItemDisabled=="disabled"  || contrItemDisabled==true){
                $("#dec_goods_list_form input[id=gName]").focus();
            }else{
                $("#dec_goods_list_form input[id=contrItem]").focus();
            }
        }else{
            $("#orgCodeName").focus();
        }
    }
}
function  enterToSpecDeclFlag(){//跳转特殊业务标识
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey && eCode == 13){//回车键
        $("#showSpecDeclFlagBtn").click();
    }
}

/**
 * 特殊业务标识-国际赛事，shift+enter
 * @param obj
 * @param e
 */
function specDeclFlag1Onblur(obj,e,ieFlag){
    var eCode = e.keyCode ? e.keyCode : e.which ? e.which : e.charCode;
    if(event.shiftKey == 1 && eCode == 13){//shiftKey+Enter键
        specDeclFlagOperate();
        layer.closeAll();
        if ("i" == ieFlag || "I" == ieFlag){
            setTimeout('$("#origBoxFlagName").focus();', 50);
        }else if ("e" == ieFlag || "E" == ieFlag){
            setTimeout('$("#correlationReasonFlagName").focus();', 50);
        }
    }else if(!event.shiftKey && eCode == 13){
        $("#specDeclFlag2").focus();
    }
}

function cusRemark4ShiftEnter(event){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(event.shiftKey && eCode == 13){
        layer.close(layerCusRemark);
        $("#noteS").focus();
    }else if(!event.shiftKey && eCode == 13){//回车键
        if($("#cusRemark1").attr("disabled") != 'disabled'){
            $("#cusRemark1").focus();
        }else if($("#cusRemark6").attr("disabled") != 'disabled'){
            $("#cusRemark6").focus();
        }else{
            layer.closeAll();
            enterToShowHeadOrGoodsList();
        }
    }
}
function enterToNext(event,foucsId){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey && eCode == 13){//回车
        $("#"+foucsId).focus();
    }
}
function specPassFlag1Onblur(event){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey && eCode == 13){//回车
        $("#specPassFlag3").focus();
    }
}
function specPassFlag3Onblur(cur,event){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey && eCode == 13){//回车
        if(cusIEFlag == "I" && cur.id == 'specPassFlag3'){
            $("#specPassFlag4").focus();
        }else{
            specDeclFlagOperate();
            layer.closeAll();
            enterToGoodsList();
        }
    }
}
function cusRemarkStatusControl(){
    /*状态控制*/
    var entryType=$("#entryType").val();//报关单类型
    var billType=$("#billType").val();//清单类型
    if(entryType == "M"){
        $("#cusRemark6").removeAttr("disabled");
        $("#cusRemark6").focus();
        /*		if(dclTrnRelFlag == "2" && cusRmftFcbFlag != "2"){//备案清单
                    if(billType == "4"){
                        $("#cusRemark1").removeAttr("disabled");
                        $("#cusRemark1").focus();
                    }else{
                        $("#cusRemark1").attr("disabled",true);
                    }
                }else{*/
        $("#cusRemark1").removeAttr("disabled");
        $("#cusRemark1").focus();
//		}
    }else{
        $("#cusRemark1").attr("disabled",true);
        $("#cusRemark6").attr("disabled",true);
    }
    if(billType){
        if(billType == "1" || billType == "4"){
            $("#cusRemark4").removeAttr("disabled");
            $("#cusRemark4").focus();
        }else{
            $("#cusRemark4").attr("disabled",true);
        }
    }

}
function independentTaxChange(curId){//自主报税
    if($("#"+curId).is(':checked')){//限制自报自缴
        $("#cusRemark1").attr("disabled",true);
    }else{
        cusRemarkStatusControl();
    }
}
//附注信息点击
$("#decAddtionBtn").click(function(){
    openWindown("附注信息",'addtionInfoLa',"#addtionLayer",['确定', '取消'],[ '450px', '305px' ],null,confirmBack,successAddtionBack,null);
});
//成功回调函数
function successAddtionBack(){
    value=$("#additionInfo").val();
    $("#additionInfoLa").focus();//默认光标定位
    $("#additionInfoLa").val(value);
    document.getElementById('addtionByteTotalLa').innerHTML =decGetlen(value,2);
}
//确定回调函数
function confirmBack(){
    value=$("#additionInfoLa").val();
    $("#additionInfo").val(value);
}
function isCopPromiseClick(obj){
    if($(obj).is(':checked')){
        $("#isCopPromise").val(1);
        if(cusIEFlag=="I"){
            $("#entPromiseTable").bootstrapTable("checkBy", {field:"entQualifTypeCode", values:["101040"]});
        }else{
            $("#entPromiseTable").bootstrapTable("checkBy", {field:"entQualifTypeCode", values:["102053"]});
        }
//		$('#entPromiseTable').bootstrapTable("checkAll");
//		$("#entPromiseDiv").css('display','block');
    }else{
        $("#isCopPromise").val(0);
        $('#entPromiseTable').bootstrapTable("uncheckAll");
//		$("#entPromiseDiv").css('display','none');
    }
}
function enterSkipNext(event,focusId){
    var eCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
    if(!event.shiftKey && eCode == 13){//回车
        if(event.ctrlKey && event.keyCode == 13){
            return;
        }
        layer.closeAll();
        enterToNext(event, focusId);
    }
}

/**
 * 引用电子底账弹窗
 * @returns
 */
function drawEntEpassRele(){
    layer.open({
        type:1,
        shadeClose:false,
        area:['540px','200px'],
        title:"引用电子底账",
        shade:0.5,
        scrollbar:false,
        btn:['引用', '取消'],//按钮名称
        btnAlign: 'c',//居中
        yes:function(index){
            var qcPassId = $("#qcPassId").val();
            if(qcPassId){
                var cusCiqNo = $("#cusCiqNo").val();
                if(cusCiqNo){
                    layer.confirm('该票已经暂存，继续引用将会覆盖本票数据，是否继续引用?', {
                        icon : 3,
                        title : '提示'
                    }, function(index) { // 3提示图标
                        layer.close(index);
                        entEpassReleAjax(qcPassId);//调用后台服务
                    }, function(index) { // 3提示图标
                        layer.closeAll();
                    });
                }else{
                    entEpassReleAjax(qcPassId);//调用后台服务
                }
            }else{
                layerOpen('提示', '电子底账号不能为空！');
            }
        },cancel:function(index){
            $("#qcPassId").val("");
            layer.closeAll();
        },
        btn2:function(index){
            $("#qcPassId").val("");
            layer.closeAll();
        },
        content:$("#drawEntEpassReleContent")
    });
}
/**
 * 电子底账调用后台服务
 * @param qcPassId
 * @returns
 */
function entEpassReleAjax(qcPassId){
    var param ={"qcPassId":qcPassId};
    $.ajax({
        type:"post",
        timeout : 40000,
        url: swProxyBasePath + "sw/dec/merge/queryEntEpassReleInfo",
        data:JSON.stringify(param),
        contentType : "application/json; charset=utf-8",
        dataType: "json",
        success: function(data) {
            if (data.ok==true) {
                var preDecHeadVo = data.data.preDecHeadVo;
                fillDecInfo(preDecHeadVo);
                layer.closeAll();
                $("#qcPassId").val("");
                layerMsg('引用电子底账成功！', 1, 2000);
            } else {
                layerOpen('提示', data.data.message);
            }
        }, error: function() {
            layerOpen('提示', '引用电子底账失败!');
        }
    })
}
$("#dec_goods_list_form input[id=ciqName]").bind('click',function(){
    if($(this).val()){
        layer.confirm('是否清空检验检疫名称?', {
            icon : 3,
            title : '提示',
            success: function(layero, index){
                $(document).on('keydown', function(e){  //document为当前元素，限制范围，如果不限制的话会一直有事件
                    if(e.keyCode == 13){
                        layer.close(index);
                        return;
                    }
                });
            }
        }, function(index) { // 3提示图标
            layer.close(index);
            $("#dec_goods_list_form input[id=ciqName]").val('');
            $("#dec_goods_list_form input[id=ciqCode]").val('');
            $("#dec_goods_list_form input[id=gName]").focus();
        }, function(index) { // 3提示图标
            layer.closeAll();
            $("#dec_goods_list_form input[id=gName]").focus();
        });
    }
});
