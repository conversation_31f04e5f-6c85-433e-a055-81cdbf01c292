import ImgPreviewComponet from "./../components/common/ImgPreview"

let ImgPreview = {}
ImgPreview.install = function (Vue) {
    Vue.prototype.$imgPreview = function (urls,idx) {
        const ImgPreviewController = Vue.extend(ImgPreviewComponet);
        let instance = new ImgPreviewController().$mount(document.createElement("div"));
        document.body.appendChild(instance.$el);
        instance.imgList = urls;
        instance.imgIndex = idx;
        instance.imgUrl = urls[idx];
        instance.showImgPreview = true;
    }
}
if(window.Vue){
    Vue.use(ImgPreview);
}
export default ImgPreview;
