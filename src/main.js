import Vue from 'vue';
import Vuex from 'vuex';
import App from './App';
import router from './router';
import axios from './util/axios';
import util from './util/util';
import imgPreview from './util/imgPreview';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';  // 默认主题
import "./../static/css/base.less";               //自定义样式
import "babel-polyfill";
import {status,filters} from './util/filter'
import cal from './util/calculation'

import country from './components/components/country'
Vue.use(country)
import channel from './components/components/channel'
Vue.use(channel)

Vue.prototype.cal = cal;

Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})

Vue.use(Vuex)
Vue.use(ElementUI, { size: 'small' });
Vue.use(imgPreview);
Vue.prototype.$axios = axios;
Vue.prototype.$util = util;
Vue.prototype.STATUS = status;
Vue.prototype.FILTER = filters;

Vue.config.productionTip = false

const store = new Vuex.Store({
    state:{
        ajaxIsLoading: false,
        isCollapse:false,
        menuParentId:'',
        menu:'',
        listParams:new Map(),
    },
    mutations:{
        ['AJAX_BEGIN_REQUEST'](state) {
            state.ajaxIsLoading = true;
        },
        ['AJAX_END_REQUEST'](state) {
            state.ajaxIsLoading = false;
        },
        ['CollapseSidebar'](state,boolean){
            if(boolean!=undefined){
                state.isCollapse = boolean;
            }else{
                state.isCollapse = !state.isCollapse;
            }

        },
        ['SetId'](state,id){
            state.menuParentId = id;
        },
        ['SetMenu'](state,menu){
            state.menu = menu;
        },
        ['SAVE_LIST_PARAMS']: (state,{ path,params }) => {

            state.listParams.set(path,params);
        },
    },
    actions:{
        saveListParams: ({ commit },{path,params}) => {
            commit('SAVE_LIST_PARAMS',{ path,params });
        },
    }
});


new Vue({
    router,
    store,
    render: h => h(App)
}).$mount('#app');

