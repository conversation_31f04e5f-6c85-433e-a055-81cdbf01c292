import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);


export default new Router({

    routes: [
        {
            path: '/login',
            component: resolve => require(['../components/page/public/Login.vue'], resolve)
        },
        {
            path: '/',
            component: resolve => require(['../components/common/Home.vue'], resolve),
            children:[
                {
                    path: '/',
                    redirect: '/home/<USER>/list',
                },
                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/account/List.vue'], resolve),
                    meta:['首页','账户授权','授权列表']
                },
                {
                    path: '/home/<USER>/edit',
                    component: resolve => require(['../components/page/account/Edit.vue'], resolve),
                    meta:['首页','账户授权','编辑']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/account/Detail.vue'], resolve),
                    meta:['首页','账户授权','详情']
                },
                {
                    path: '/home/<USER>/log',
                    component: resolve => require(['../components/page/account/Log.vue'], resolve),
                    meta:['首页','账户授权','异动记录']
                },


                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/message/List.vue'], resolve),
                    meta:['首页','消息管理','全部消息']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/message/Detail.vue'], resolve),
                    meta:['首页','消息管理','消息详情']
                },
                {
                    path: '/home/<USER>/edit',
                    component: resolve => require(['../components/page/message/Edit.vue'], resolve),
                    meta:['首页','消息管理','发布消息']
                },

                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/order/List.vue'], resolve),
                    meta:['首页','运单管理','全部运单']
                },


                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/product/List.vue'], resolve),
                    meta:['首页','商品管理','商品维护']
                },

                {
                    path: '/home/<USER>/jn',
                    component: resolve => require(['../components/page/customer/Jn.vue'], resolve),
                    meta:['首页','客户管理','境内收发货人']
                },
                {
                    path: '/home/<USER>/jw',
                    component: resolve => require(['../components/page/customer/Jw.vue'], resolve),
                    meta:['首页','客户管理','境外收发货人']
                },



                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/system/Feedback.vue'], resolve),
                    meta:['首页','系统管理','建议与反馈']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/system/Feedback-detail.vue'], resolve),
                    meta:['首页','系统管理','建议与反馈']
                },



                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/welcome',
                },
                {
                    path: '/home/<USER>/welcome',
                    component: resolve => require(['../components/page/index/Welcome.vue'], resolve),
                    meta:['首页','首页','欢迎页']
                },
                {
                    path: '/home/<USER>/notice',
                    component: resolve => require(['../components/page/index/Notice.vue'], resolve),
                    meta:['首页','首页','系统公告']
                },
                {
                    path: '/home/<USER>/notice/detail',
                    component: resolve => require(['../components/page/index/Notice-detail.vue'], resolve),
                    meta:['首页','首页','系统公告详情']
                },
                {
                    path: '/home/<USER>/message',
                    component: resolve => require(['../components/page/index/Message.vue'], resolve),
                    meta:['首页','首页','系统消息']
                },
                {
                    path: '/home/<USER>/message/detail',
                    component: resolve => require(['../components/page/index/Message-detail.vue'], resolve),
                    meta:['首页','首页','系统消息详情']
                },



                //系统消息与公告
                {
                    path: '/home/<USER>/message',
                    component: resolve => require(['../components/page/system/Message.vue'], resolve),
                    meta:['首页','系统','消息']
                },
                {
                    path: '/home/<USER>/message/detail',
                    component: resolve => require(['../components/page/system/Message-detail.vue'], resolve),
                    meta:['首页','系统','消息详情']
                },



                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/authority',
                },
                {
                    path: '/home/<USER>/authority',
                    component: resolve => require(['../components/page/role/Authority.vue'], resolve),
                    meta:['首页','角色权限','角色权限']
                },
                {
                    path: '/home/<USER>/authority/edit',
                    component: resolve => require(['../components/page/role/Authority-edit.vue'], resolve),
                    meta:['首页','角色权限','角色权限']
                },
                {
                    path: '/home/<USER>/user',
                    component: resolve => require(['../components/page/role/User.vue'], resolve),
                    meta:['首页','角色权限','职员列表']
                },
                {
                    path: '/home/<USER>/user/edit',
                    component: resolve => require(['../components/page/role/User-edit.vue'], resolve),
                    meta:['首页','角色权限','角色权限']
                },
                {
                    path: '/home/<USER>/config',
                    component: resolve => require(['../components/page/role/Config.vue'], resolve),
                    meta:['首页','角色权限','参数管理']
                },
                {
                    path: '/home/<USER>/syslog',
                    component: resolve => require(['../components/page/role/Syslog.vue'], resolve),
                    meta:['首页','角色权限','系统日志']
                },



                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/list',
                },
                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/trade/List.vue'], resolve),
                    meta:['首页','一般贸易','一般贸易']
                },
                {
                    path: '/home/<USER>/simple',
                    name:'simple',
                    component: resolve => require(['../components/page/trade/Simple.vue'], resolve),
                    meta:['首页','一般贸易','简易新增']
                },
                {
                    path: '/home/<USER>/expert',
                    name:'expert',
                    component: resolve => require(['../components/page/trade/Expert.vue'], resolve),
                    meta:['首页','一般贸易','专家新增']
                },
                {
                    path: '/home/<USER>/declared',
                    name:'declared',
                    component: resolve => require(['../components/page/trade/Declared.vue'], resolve),
                    meta:['首页','一般贸易','待申报']
                },
                {
                    path: '/home/<USER>/confirm',
                    name:'confirm',
                    component: resolve => require(['../components/page/trade/Confirm.vue'], resolve),
                    meta:['首页','一般贸易','待确认']
                },
                {
                    path: '/home/<USER>/original-detail',
                    component: resolve => require(['../components/page/trade/OriginalDetail.vue'], resolve),
                    meta:['首页','一般贸易','申报前详情']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/trade/Detail.vue'], resolve),
                    meta:['首页','一般贸易','申报后详情']
                },


                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/oftenProduct',
                },
                {
                    path: '/home/<USER>/oftenProduct',
                    component: resolve => require(['../components/page/basic/OftenProduct.vue'], resolve),
                    meta:['首页','基础设置','常用商品']
                },
                {
                    path: '/home/<USER>/oftenProduct/edit',
                    component: resolve => require(['../components/page/basic/Product-edit.vue'], resolve),
                    meta:['首页','基础设置','常用商品']
                },
                {
                    path: '/home/<USER>/abroadconinfo',
                    component: resolve => require(['../components/page/basic/Abroadconinfo.vue'], resolve),
                    meta:['首页','基础设置','境内收货人']
                },
                {
                    path: '/home/<USER>/abroadconinfo/edit',
                    component: resolve => require(['../components/page/basic/Abroadconinfo-edit.vue'], resolve),
                    meta:['首页','基础设置','境内收货人']
                },
                {
                    path: '/home/<USER>/productsellunits',
                    component: resolve => require(['../components/page/basic/Productsellunits.vue'], resolve),
                    meta:['首页','基础设置','生产销售单位']
                },
                {
                    path: '/home/<USER>/productsellunits/edit',
                    component: resolve => require(['../components/page/basic/Productsellunits-edit.vue'], resolve),
                    meta:['首页','基础设置','生产销售单位']
                }
            ]
        }
    ],
    mode: "hash"
})
