<template>
    <el-select v-model="cId" :countryName="countryName" :countryId="countryId" filterable placeholder="请选择" @change="handleChange">
        <el-option
            v-for="item in list"
            :key="item.adcode"
            :label="item.name"
            default-first-option
            :value="item.adcode">
        </el-option>
    </el-select>
</template>

<script>
    export default {
        name: "Country",
        props:{
            countryId:{
                default:''
            },
            countryName:{
                default:''
            },
        },
        watch:{
            countryId(newVal, oldVal){
                if(newVal&&(newVal!=oldVal)){
                    if(this.countryId)this.cId=this.countryId;
                    if(this.countryName)this.cName=this.countryName;
                }
            }
        },
        data(){
            return{
                cId:'',
                cName:'',
                list:[
                    {}
                ],
            }
        },
        mounted(){
            this.getList();
            this.cId=this.countryId;
            this.cName=this.countryName;
        },
        methods:{
            getList(){
                this.$axios({url:'/sys/area/list',method:'post',data:{parenCode: 0,areaType:3}, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.list = res.areaList;
                    }
                })
            },
            handleChange(vId){
                let obj = {};
                obj = this.list.find((item)=>{
                    return item.adcode === vId;
                });
                if(obj){
                    this.cName = obj.name;
                }else{
                    this.cId=''
                    this.cName=''
                }
                this.$emit('change',[this.cId,this.cName]);
            }
        }
    }
</script>

<style scoped>

</style>
