<template>

</template>
<script>
    export default {
        data() {
            return {
                menus:[]
            }
        },


        methods: {
            handleOpen(key, keyPath) {
                console.log(key, keyPath);
            },
            handleClose(key, keyPath) {
                console.log(key, keyPath);
            },
            getMenus(){
                let self  =this;
                self.$axios.get('/aquaman/sys/menu/nav').then((res) => {
                    debugger
                    let menus = res.data;
                    menus.forEach(ele => {
                        let parentId = ele.parenId;
                        if (parentId == 0) {
                        } else {
                            menus.forEach(d => {
                                if (d.id == parentId) {
                                    let childArray = d.child;
                                    if (!childArray) {
                                        childArray = []
                                    }
                                    childArray.push(ele);
                                    d.child = childArray;
                                }
                            })
                        }
                    });
                    //去除重复元素
                    menus = menus.filter(ele => ele.parenId === 0);
                    self.menus = menus;
                });
            },
        },
        computed:{
            onRoutes(){
                return this.$route.path;
            },
            isCollapse(){
                return this.$store.state.isCollapse;
            },
            userName(){
                return localStorage.getItem('userName')
            }
        }
    }
</script>

<style scoped lang="less" ref="stylesheet/less">

    .sidebar{
        position: absolute;
        top: 70px;
        left: 0;
        background: #2E363F;
        overflow-y: auto;
        bottom: 0;
        ul {
            min-height:100%;
            border-right: none;
        }
    }
    .el-menu-vertical-demo:not(.el-menu--collapse) {
        width: 200px;
        min-height: 100%;
    }
</style>
