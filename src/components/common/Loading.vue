<template>
    <div id="loading-container" v-show="ajaxIsLoading">
        <div id="loading" >
            <img src="./../../assets/loading.gif" alt="loading">
        </div>
    </div>
</template>
<script>
    export default {
        name: 'loading',
        computed: {
            ajaxIsLoading(){
                return this.$store.state.ajaxIsLoading;
            }
        }
    }
</script>
<style scoped lang="less" rel="stylesheet/less">
    #loading-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: .3;
        background: #ccc;
        z-index: 10000;
    }

    #loading {
        position: absolute;
        left: 50%;
        top: 50%;
        width: 40px;
        height: 40px;
        z-index: 100001;
        img{
            width: 100%;
        }
    }
</style>
