<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.pid">编辑</span><span v-else>新建</span>商品</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="HSCODE:" prop="hsCode">
                            <el-input v-model="ruleForm.hsCode" maxlength="10" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="商品名称:" prop="productName">
                            <el-input v-model="ruleForm.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="申报要素:">
                            <el-input v-model="property" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="成交单位:" prop="cjUnit">
                            <v-Select
                                v-model="ruleForm.cjUnit"
                                :options="cjUnit.options" placeholder="" :params="cjUnit.params" :searchMethod="searchUnit" :change="changeCjUnit"
                            ></v-Select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="法定单位:" prop="faOneUnit">
                            <v-Select
                                v-model="ruleForm.faOneUnit"
                                :options="cjUnit.options" placeholder="" :params="cjUnit.params" :searchMethod="searchUnit" :change="changeFaOneUnit"
                            ></v-Select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="法定单位2:" prop="faTwoUnit">
                            <v-Select
                                v-model="ruleForm.faTwoUnit"
                                :options="cjUnit.options" placeholder="" :params="cjUnit.params" :searchMethod="searchUnit" :change="changeFaTwoUnit"
                            ></v-Select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="最惠(%):" prop="zuiHui">
                            <el-input v-model="ruleForm.zuiHui"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="普通(%):" prop="general">
                            <el-input v-model="ruleForm.general"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="增值税率:" prop="taxRate">
                            <el-input v-model="ruleForm.taxRate"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="监管代码:" prop="supCode">
                            <el-input v-model="ruleForm.supCode"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <!--<el-col :span="12">-->
                    <!--<el-form-item label="年份:" prop="keyYear">-->
                    <!--<el-input-number v-model="ruleForm.keyYear" :min="2018" :max="2099"></el-input-number>-->
                    <!--</el-form-item>-->
                    <!--</el-col>-->
                    <el-col :span="12">
                        <el-form-item label="状态:" prop="productStatus">
                            <el-radio v-model="ruleForm.productStatus" label="1">启用</el-radio>
                            <el-radio v-model="ruleForm.productStatus" label="0">停用</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
    import vSelect from './../public/SearchSelect';
    // import {cjUnit} from './../../../util/config';
    export default {
        data: function () {
            return {
                ruleForm: {
                    pid:'',
                    hsCode:'',
                    productName:'',
                    numWeight:'',
                    faOneUnit:'',
                    faOneUnitName:'',
                    faTwoUnit:'',
                    faTwoUnitName:'',
                    cjUnit:'',
                    cjUnitName:'',
                    oneKgNum:'',
                    maxPrice :'',
                    minPrice :'',
                    maxWeight :'',
                    minWeight :'',
                    avgPrice:'',
                    avgWeight:'',
                    passOrder:'0',
                    rate:'0',
                    zuiHui:'',
                    general:'',
                    taxRate:'',
                    supCode:'',
                    productStatus:'1',
                    keyYear:'2018',
                },
                property:'请在列表页双击修改',
                options:[
                    {value:'0',label:'否'},
                    {value:'1',label:'是'}
                ],
                cjUnit:{
                    options:cjUnit,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                rules: {
                    hsCode: [
                        { required: true, message: '请填写HSCODE', trigger: 'blur' },
                        { pattern:/^\d{10,}$/, message: '请填写10位数字的HSCODE', trigger: 'blur'}
                    ],
                    productName: [
                        { required: true, message: '请填写商品名称', trigger: 'blur' }
                    ],
                    faOneUnit: [
                        { required: true, message: '请选择法定单位', trigger: 'blur' }
                    ],
                    cjUnit: [
                        { required: true, message: '请选择成交单位', trigger: 'blur' }
                    ],
                    numWeight:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    maxPrice:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    maxWeight:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    minPrice:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    minWeight:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    avgPrice:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    avgWeight:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    zuiHui:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    general:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    taxRate:[
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    oneKgNum: [
                        { required: true, message: '请填写每千克成交量', trigger: 'blur' },
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    keyYear: [
                        { required: true, message: '请填写年份', trigger: 'blur' }
                    ],
                },
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.pid = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let self = this,url = '/trade/tradeproductinfo/getProductInfo/'+self.ruleForm.pid;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        let obj = res.productInfo;
                        self.ruleForm={
                            pid:obj.pid,
                            hsCode:obj.hsCode,
                            productName:obj.productName,
                            numWeight:obj.numWeight,
                            faOneUnit:obj.faOneUnit?obj.faOneUnit:'',
                            faOneUnitName:obj.faOneUnitName,
                            faTwoUnit:obj.faTwoUnit?obj.faTwoUnit:'',
                            faTwoUnitName:obj.faTwoUnitName,
                            cjUnit:obj.cjUnit?obj.cjUnit:'',
                            cjUnitName:obj.cjUnitName,
                            oneKgNum:obj.oneKgNum,
                            maxPrice:obj.maxPrice,
                            minPrice:obj.minPrice,
                            maxWeight:obj.maxWeight,
                            minWeight:obj.minWeight,
                            avgPrice:obj.avgPrice,
                            avgWeight:obj.avgWeight,
                            passOrder:''+obj.passOrder,
                            rate:''+obj.rate,
                            zuiHui:obj.zuiHui,
                            general:obj.general,
                            taxRate:obj.taxRate,
                            supCode:obj.supCode,
                            productStatus:obj.productStatus?''+obj.productStatus:'1',
                            keyYear:obj.keyYear?obj.keyYear:'2018',
                        };
                    }
                });
            },

            searchUnit(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeFaOneUnit(vId){
                let obj = {};
                obj = this.cjUnit.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.faOneUnitName = obj.label.split('-')[1];
            },
            changeFaTwoUnit(vId){
                let obj = {};
                obj = this.cjUnit.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.faTwoUnitName = obj.label.split('-')[1];
            },
            changeCjUnit(vId){
                let obj = {};
                obj = this.cjUnit.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.cjUnitName = obj.label.split('-')[1];
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url;
                        if(self.ruleForm.pid){
                            url ='/trade/tradeproductinfo/update';
                        }else{
                            url ='/trade/tradeproductinfo/save';
                        }
                        self.$axios.post(url,self.ruleForm).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        },
        components:{
            vSelect
        }
    }
</script>
