<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.abId">编辑</span><span v-else>新建</span>境内收发货人</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-form-item label="境内收发货人名称:" prop="abName">
                    <el-input v-model="ruleForm.abName"></el-input>
                </el-form-item>
                <el-form-item label="海关十位代码:" prop="customsCode">
                    <el-input v-model="ruleForm.customsCode" maxlength="10"></el-input>

                </el-form-item>
                <el-form-item label="统一社会信用代码:" prop="unifyCode" >
                    <el-input v-model="ruleForm.unifyCode"></el-input>
                </el-form-item>
                <el-form-item label="商检十位码:" prop="coTenCode">
                    <el-input v-model="ruleForm.coTenCode" maxlength="10"></el-input>
                </el-form-item>
                <el-form-item label="法人姓名:" prop="principal">
                    <el-input v-model="ruleForm.principal"></el-input>
                </el-form-item>
                <el-form-item label="电话:" prop="mobile">
                    <el-input v-model="ruleForm.mobile"></el-input>
                </el-form-item>
                <el-form-item label="公章图片:" prop="commonImg">
                    <el-upload
                        class="avatar-uploader"
                        action="/aquaman/trade/file/uploads"
                        name="files"
                        :data="{fileType:2,orderNum:''}"
                        :headers="header"
                        :show-file-list="false"
                        :on-success="handleSuccess1"
                        :on-error="handleError"
                        :on-progress="handleProgress"
                        :before-upload="beforeUpload">
                        <img v-if="ruleForm.commonImg" :src="ruleForm.commonImg" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="法人章图片:" prop="sealImg">
                    <el-upload
                        class="avatar-uploader"
                        action="/aquaman/trade/file/uploads"
                        name="files"
                        :data="{fileType:2,orderNum:''}"
                        :headers="header"
                        :show-file-list="false"
                        :on-success="handleSuccess2"
                        :on-error="handleError"
                        :on-progress="handleProgress"
                        :before-upload="beforeUpload">
                        <img v-if="ruleForm.sealImg" :src="ruleForm.sealImg" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="是否默认:" prop="isDefault">
                    <el-radio-group v-model="ruleForm.isDefault">
                        <el-radio label="0">否</el-radio>
                        <el-radio label="1">是</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
    let token =  localStorage.getItem('adminToken');
    export default {
        data: function () {
            return {
                ruleForm: {
                    abId:'',
                    abName: '',
                    customsCode: '',
                    unifyCode: '',
                    coTenCode:'',
                    principal: '',
                    mobile: '',
                    commonImg: '',
                    sealImg: '',
                    isDefault: '0'
                },
                rules: {
                    abName: [
                        { required: true, message: '请填写境内收发货人名称', trigger: 'blur' }
                    ],
                    customsCode: [
                        { required: true, message: '请填写海关十位代码', trigger: 'blur' }
                    ],
                    coTenCode: [
                        { required: true, message: '请填写商检十位码', trigger: 'blur' }
                    ],
                    mobile: [
                        { pattern:/^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                    ],
                    // commonImg: [
                    //     { required: true, message: '请上传公章图片', trigger: 'change' }
                    // ],
                    // sealImg: [
                    //     { required: true, message: '请上传法人章图片', trigger: 'change' }
                    // ],
                    isDefault: [
                        { required: true, message: '请选择是否默认', trigger: 'change' }
                    ],
                },
                loading:'',
                header: {token: token},
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.abId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let self = this,url = '/trade/abroadconinfo/info/'+self.ruleForm.abId;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        self.ruleForm={
                            abId:res.abroadConInfo.abId,
                            abName: res.abroadConInfo.abName,
                            customsCode: res.abroadConInfo.customsCode,
                            unifyCode: res.abroadConInfo.unifyCode,
                            coTenCode: res.abroadConInfo.coTenCode,
                            principal: res.abroadConInfo.principal,
                            mobile: res.abroadConInfo.mobile,
                            commonImg: res.abroadConInfo.commonImg,
                            sealImg: res.abroadConInfo.sealImg,
                            isDefault: ''+res.abroadConInfo.isDefault
                        };
                    }
                });
            },
            handleProgress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '正在上传图片...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            handleError(){
                this.loading.close();
                this.$message({
                    message: "上传失败,请重试",
                    type: 'error'
                })
            },
            handleSuccess1(res, file) {
                this.loading.close();
                this.ruleForm.commonImg = res.result;
            },
            handleSuccess2(res, file) {
                this.loading.close();
                this.ruleForm.sealImg = res.result;
            },
            beforeUpload(file) {
                const isJPG = (file.type).indexOf("image/")!=-1
                const isLt = file.size / 1024 / 1024 < 10;

                if (!isJPG) {
                    this.$message.error('请上传正确的图片格式!');
                }
                if (!isLt) {
                    this.$message.error('上传图片大小不能超过10MB!');
                }
                return isJPG && isLt;
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.abId?'/trade/abroadconinfo/update':'/trade/abroadconinfo/save';
                        self.$axios.post(url,self.ruleForm).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
