<template>
    <div>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    username:'',
                    sidx:'',
                    order:'',
                },
                rowKey:'abId',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { prop: 'abId', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'isDefault', label: '默认',sortable:'custom',formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; } ,width:"70px"},
                    { prop: 'abName', label: '境内收发货人名称',sortable:'custom',minWidth:"200px"},
                    { prop: 'customsCode', label: '海关十位代码',sortable:'custom',minWidth:"120px"},
                    { prop: 'unifyCode', label: '统一社会信用代码',sortable:'custom',minWidth:"150px"},
                    { prop: 'coTenCode', label: '商检十位码',sortable:'custom',minWidth:"120px"},
                    { prop: 'principal', label: '法人姓名',sortable:'custom',width:"100px"},
                    { prop: 'mobile', label: '电话',sortable:'custom',width:"100px"},
                    { prop: 'commonImg',download:true, label: '公章图片',sortable:'custom',width:"100px"},
                    { prop: 'sealImg',download:true, label: '法人章图片',sortable:'custom',width:"120px"},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { prop: 'abId', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'isDefault', label: '默认',sortable:'custom',formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; } ,width:"70px"},
                    { prop: 'abName', label: '境内收发货人名称',sortable:'custom',minWidth:"200px"},
                    { prop: 'customsCode', label: '海关十位代码',sortable:'custom',minWidth:"120px"},
                    { prop: 'unifyCode', label: '统一社会信用代码',sortable:'custom',minWidth:"150px"},
                    { prop: 'coTenCode', label: '商检十位码',sortable:'custom',minWidth:"120px"},
                    { prop: 'principal', label: '法人姓名',sortable:'custom',width:"100px"},
                    { prop: 'mobile', label: '电话',sortable:'custom',width:"100px"},
                    { prop: 'commonImg',download:true, label: '公章图片',sortable:'custom',width:"100px"},
                    { prop: 'sealImg',download:true, label: '法人章图片',sortable:'custom',width:"120px"},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/trade/abroadconinfo/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/abroadconinfo/edit',query:{id:row.abId}})
            },
            handleDelete(row){
                let self = this;
                self.$confirm('是否确认删除该境内收货人?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/abroadconinfo/delete',[row.abId]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vTable
        }
    }
</script>

