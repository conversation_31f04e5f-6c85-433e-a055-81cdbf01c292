<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.spId">编辑</span><span v-else>新建</span>生产销售单位</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-form-item label="生产销售单位名称:" prop="spName">
                    <el-input v-model="ruleForm.spName"></el-input>
                </el-form-item>
                <el-form-item v-if="!isCustomsCode" label="海关十位代码:" prop="customsCode">
                    <el-input v-model="ruleForm.customsCode" maxlength="10"></el-input>

                </el-form-item>
                <el-form-item v-if="isCustomsCode" label="组织机构代码:" prop="organCode">
                    <el-input disabled v-model="ruleForm.organCode"></el-input>
                </el-form-item>
                <el-form-item label="" prop="isCustomsCode">
                    <el-checkbox @change="changeCustomsCode" v-model="isCustomsCode">无海关代码</el-checkbox>
                </el-form-item>
                <el-form-item label="统一社会信用代码:" prop="unifyCode" >
                    <el-input @change="changeOrganCode" v-model="ruleForm.unifyCode"></el-input>
                </el-form-item>
                <el-form-item label="商检十位码:" prop="coTenCode">
                    <el-input v-model="ruleForm.coTenCode" maxlength="10"></el-input>
                </el-form-item>
                <el-form-item label="法人姓名:" prop="principal">
                    <el-input v-model="ruleForm.principal"></el-input>
                </el-form-item>
                <el-form-item label="电话:" prop="mobile">
                    <el-input v-model="ruleForm.mobile"></el-input>
                </el-form-item>
                <el-form-item label="是否默认:" prop="isDefault">
                    <el-radio-group v-model="ruleForm.isDefault">
                        <el-radio label="0">否</el-radio>
                        <el-radio label="1">是</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>

    export default {
        data: function () {
            return {
                ruleForm: {
                    spId:'',
                    spName: '',
                    customsCode: '',
                    unifyCode: '',
                    coTenCode:'',
                    organCode: '',
                    principal: '',
                    mobile: '',
                    isDefault: '0'
                },
                isCustomsCode:false,
                rules: {
                    spName: [
                        { required: true, message: '请填写生产销售单位名称', trigger: 'blur' }
                    ],
                    customsCode: [
                        { required: true, message: '请填写海关十位代码', trigger: 'blur' }
                    ],
                    organCode: [
                        { required: true, message: '请填写组织机构代码', trigger: 'blur' }
                    ],
                    unifyCode: [
                        { required: true, message: '请填写统一社会信用代码 ', trigger: 'blur' }
                    ],
                    mobile: [
                        { pattern:/^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                    ],
                    isDefault: [
                        { required: true, message: '请选择是否默认', trigger: 'change' }
                    ],
                },
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.spId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let self = this,url = '/trade/productsellunits/info/'+self.ruleForm.spId;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        self.ruleForm={
                            spId:res.productSellUnits.spId,
                            spName: res.productSellUnits.spName,
                            customsCode: res.productSellUnits.customsCode,
                            unifyCode: res.productSellUnits.unifyCode,
                            coTenCode: res.productSellUnits.coTenCode,
                            organCode: res.productSellUnits.organCode,
                            principal: res.productSellUnits.principal,
                            mobile: res.productSellUnits.mobile,
                            isDefault: ''+res.productSellUnits.isDefault
                        };
                        if(self.ruleForm.customsCode==''){
                            self.isCustomsCode=true;
                        }
                    }
                });
            },
            changeCustomsCode(val){
                if(val){
                    this.ruleForm.customsCode='';
                    this.changeOrganCode();
                }else{
                    this.ruleForm.organCode='';
                }
            },
            changeOrganCode(){
                let str = this.ruleForm.unifyCode;
                if(str.length>=10&&this.isCustomsCode){
                    str = str.substring(str.length-10);
                    str = str.substring(0,str.length-1);
                    this.ruleForm.organCode = str;
                }
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.spId?'/trade/productsellunits/update':'/trade/productsellunits/save';
                        self.$axios.post(url,self.ruleForm).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
