<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="商品名称">
                        <el-input v-model="params.productName" placeholder="请输入商品名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="HSCODE">
                        <el-input v-model="params.hsCode" placeholder="请输入HSCODE"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="商品状态">
                        <el-select v-model="params.productStatus" placeholder="请选择">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="正常" value="1"></el-option>
                            <el-option label="停用" value="0"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="addProductDialog">新增</el-button>
            </el-row>
            <el-tabs type="card" v-model="params.productType" @tab-click="changeType">
                <el-tab-pane label="默认常用"  name="1"></el-tab-pane>
                <el-tab-pane label="最近常用" name="0"></el-tab-pane>
            </el-tabs>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>
        <v-dialog :dialogVisible="dialogVisible" :cancelDialog="cancelDialog" :enterDialog="enterDialog" @dialogStatus="dialogStatus" :title="title">
            <template slot="dialog">
                <el-form ref="form" :model="form" v-enterToNext="true">
                    <table class="h-table">
                        <tr>
                            <td class="name">商品信息</td>
                            <td>{{product.hsCode}}-{{product.productName}}</td>
                        </tr>
                        <tr>
                            <td colspan="2"><strong>规格型号(根据海关规定,以下要素应全部填报)</strong></td>
                        </tr>
                        <template v-if="property.length>0">
                            <tr v-for="(item,index) in property">
                                <td><i v-if="item.required" class="require">*</i>{{item.propertyName}}</td>
                                <td>
                                    <el-input ref="propertyKey" v-model="property[index].productValue" v-if="property[index].optionValue=='无'||!property[index].optionValue"></el-input>
                                    <el-select v-model="property[index].productValue" placeholder="请选择"  filterable default-first-option v-else>
                                        <el-option v-for="(item,index) in property[index].optionValue.split(',')" :key="item+index" :label="item" :value="item">
                                        </el-option>
                                    </el-select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">规格型号:{{specification}}</td>
                            </tr>
                        </template>
                        <tr v-else>
                            <td colspan="2">暂无申报要素</td>
                        </tr>
                    </table>
                </el-form>
            </template>
        </v-dialog>
        <el-dialog @close="closeDialog1" custom-class="h-dialog" title="选择商品" :visible.sync="dialogVisible1">
            <div class="mb_1">
                <el-input style="width: 300px" placeholder="请输入商品名称或HS编码" prefix-icon="el-icon-search" v-model="params1.keyWord"></el-input>
                <el-radio v-model="params1.kuType" label="1">公共库</el-radio>
                <el-radio v-model="params1.kuType" label="2">国标库</el-radio>
                <el-button type="primary" class="" @click="addProductDialog">查询</el-button>
            </div>
            <el-table :data="tableData1" row-key="pid" border style="width: 100%" ref="multipleTable" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" align="center">
                </el-table-column>
                <el-table-column min-width="70" prop="hsCode" label="HSCODE" align="center">
                </el-table-column>
                <el-table-column min-width="200" prop="productName" label="商品名称" align="center">
                </el-table-column>
                <el-table-column min-width="150" prop="property" label="申报要素" align="center">
                </el-table-column>
                <el-table-column min-width="50" prop="cjUnit" label="单位" align="center">
                </el-table-column>
                <el-table-column min-width="50" prop="productStatus" label="状态" align="center" :formatter="formatStatus">
                </el-table-column>
            </el-table>
            <div class="text-center mt_2">
                <el-pagination
                    @size-change="handleSizeChange1"
                    @current-change="handleCurrentChange1"
                    :current-page="params1.page"
                    :page-sizes="[50, 100, 300, 500]"
                    :page-size="params1.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total1">
                </el-pagination>
            </div>
            <div slot="footer" class="dialog-footer text-center">
                <el-button @click="cancelDialog1">取 消</el-button>
                <el-button type="primary" @click="enterDialog1">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';
    import vDialog from './../public/Dialog';

    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit:50,
                    sidx:'',
                    order:'',
                    hsCode:'',
                    productName:'',
                    productStatus:'',
                    productType:'1',
                },
                params1:{
                    page:1,
                    limit:50,
                    sidx:'',
                    order:'',
                    keyWord:'',
                    kuType:'1',
                },
                multipleSelection:'',
                title:'商品申报要素',
                property:[],
                product:{},
                form:{

                },
                rowKey:'pid',
                dialogVisible:false,
                dialogVisible1:false,
                total:0,
                total1:0,
                totalPage:1,
                totalPage1:1,
                tableData: [],
                tableData1: [],
                tableHeader: [
                    { prop: 'pid', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',width:"100px"},
                    { prop: 'productName', label: '商品名',sortable:'custom',width:"160px"},
                    { prop: 'property', label: '申报要素',sortable:'custom',width:"300px",custom:this.dbClick},
                    { prop: 'numWeight', label: '数重比',sortable:'custom',width:"90px"},
                    { prop: 'faOneUnit', label: '第一法定单位',sortable:'custom',width:"120px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'faTwoUnit', label: '第二单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'cjUnit', label: '成交单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'oneKgNum', label: '每千克成交量',sortable:'custom',width:"120px"},
                    { prop: 'avgPrice', label: '平均单价',sortable:'custom',width:"100px"},
                    { prop: 'avgWeight', label: '平均净重',sortable:'custom',width:"100px"},
                    { prop: 'passOrder', label: '涉通关单',sortable:'custom',width:"100px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'rate', label: '涉税',sortable:'custom',width:"70px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'productStatus', label: '状态',width:"70px",sortable:'custom', formatData: function(val) { return val == 0 ? '已停用' :val == 1 ? '已开启' : '未知'; } },
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { prop: 'pid', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom'},
                    { prop: 'productName', label: '商品名',sortable:'custom'},
                    { prop: 'property', label: '申报要素',sortable:'custom',custom:this.dbClick},
                    { prop: 'numWeight', label: '数重比',sortable:'custom'},
                    { prop: 'faOneUnit', label: '第一法定单位',sortable:'custom',width:"120px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'faTwoUnit', label: '第二单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'cjUnit', label: '成交单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'oneKgNum', label: '每千克成交量',sortable:'custom'},
                    { prop: 'avgPrice', label: '平均单价',sortable:'custom'},
                    { prop: 'avgWeight', label: '平均净重',sortable:'custom'},
                    { prop: 'passOrder', label: '涉通关单',sortable:'custom',formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'rate', label: '涉税',sortable:'custom',formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'productStatus', label: '状态',sortable:'custom', formatData: function(val) { return val == 0 ? '已停用' :val == 1 ? '已开启' : '未知'; } },
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
            }
        },
        computed:{
            specification:function(){
                let property='',len = this.property?this.property.length:0;
                for(let i=0;i< len;i++){
                    if(i==len-1){
                        property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue:'';
                    }else{
                        property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue+'|':'';
                    }
                }
                return property
            },
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/trade/tradeproductinfo/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            searchList(){
                let self = this;
                self.$axios.get('/trade/tradeproductinfo/searchStateHsCode',{params: self.params1}).then((res) => {
                    self.tableData1 = res.page.list;
                    self.total1 = parseInt(res.page.totalCount);
                    self.totalPage1 = parseInt(res.page.totalPage);

                });
            },
            formatStatus(row){
                return row.productStatus == 0 ? '已停用' :row.productStatus == 1 ? '已启用' : '未知';
            },
            addProductDialog(){
                this.dialogVisible1 = true;
                this.searchList();
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            cancelDialog1(){
                this.dialogVisible1 = false;
            },
            closeDialog1(){
                this.dialogVisible1 = false;
            },
            enterDialog1(){
                let self = this,ids=[];
                self.dialogVisible1 = false;
                if(self.multipleSelection==undefined||self.multipleSelection==''){
                    self.$message({type: 'error', message: '您没有添加任何商品'});
                    return false;
                }else{
                    self.multipleSelection.map((item)=>{
                        ids.push(item.pid)
                    });
                }
                self.$axios.post('/trade/tradeproductinfo/save',{'pids':ids,'kuType':self.params1.kuType}).then((res) => {
                    if(res.code==0){
                        self.$message({
                            type: 'success',
                            message: '添加成功'
                        });
                        self.getList();
                    }
                });

            },
            changeType(){
                this.getList();
                this.tableHeader = this.params.productType==1?[
                    { prop: 'pid', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',width:"100px"},
                    { prop: 'productName', label: '商品名',sortable:'custom',width:"160px"},
                    { prop: 'property', label: '申报要素',sortable:'custom',width:"300px",custom:this.dbClick},
                    { prop: 'numWeight', label: '数重比',sortable:'custom',width:"90px"},
                    { prop: 'faOneUnit', label: '第一法定单位',sortable:'custom',width:"120px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'faTwoUnit', label: '第二单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'cjUnit', label: '成交单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'oneKgNum', label: '每千克成交量',sortable:'custom',width:"120px"},
                    { prop: 'avgPrice', label: '平均单价',sortable:'custom',width:"100px"},
                    { prop: 'avgWeight', label: '平均净重',sortable:'custom',width:"100px"},
                    { prop: 'passOrder', label: '涉通关单',sortable:'custom',width:"100px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'rate', label: '涉税',sortable:'custom',width:"70px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'productStatus', label: '状态',width:"70px",sortable:'custom', formatData: function(val) { return val == 0 ? '已停用' :val == 1 ? '已开启' : '未知'; } },
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ]:[
                    { prop: 'pid', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',width:"100px"},
                    { prop: 'productName', label: '商品名',sortable:'custom',width:"160px"},
                    { prop: 'property', label: '申报要素',sortable:'custom',width:"300px",custom:this.dbClick},
                    { prop: 'numWeight', label: '数重比',sortable:'custom',width:"90px"},
                    { prop: 'faOneUnit', label: '第一法定单位',sortable:'custom',width:"120px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'faTwoUnit', label: '第二单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'cjUnit', label: '成交单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'oneKgNum', label: '每千克成交量',sortable:'custom',width:"120px"},
                    { prop: 'avgPrice', label: '平均单价',sortable:'custom',width:"100px"},
                    { prop: 'avgWeight', label: '平均净重',sortable:'custom',width:"100px"},
                    { prop: 'passOrder', label: '涉通关单',sortable:'custom',width:"100px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'rate', label: '涉税',sortable:'custom',width:"70px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'usageCounter', label: '使用次数',sortable:'custom',width:"100px"},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'plus', clickFun: this.handleAdd },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ];
                this.dropTableHeader = this.params.productType==1?[
                    { prop: 'pid', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',width:"100px"},
                    { prop: 'productName', label: '商品名',sortable:'custom',width:"160px"},
                    { prop: 'property', label: '申报要素',sortable:'custom',width:"300px",custom:this.dbClick},
                    { prop: 'numWeight', label: '数重比',sortable:'custom',width:"90px"},
                    { prop: 'faOneUnit', label: '第一法定单位',sortable:'custom',width:"120px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'faTwoUnit', label: '第二单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'cjUnit', label: '成交单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'oneKgNum', label: '每千克成交量',sortable:'custom',width:"120px"},
                    { prop: 'avgPrice', label: '平均单价',sortable:'custom',width:"100px"},
                    { prop: 'avgWeight', label: '平均净重',sortable:'custom',width:"100px"},
                    { prop: 'passOrder', label: '涉通关单',sortable:'custom',width:"100px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'rate', label: '涉税',sortable:'custom',width:"70px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'productStatus', label: '状态',width:"70px",sortable:'custom', formatData: function(val) { return val == 0 ? '已停用' :val == 1 ? '已开启' : '未知'; } },
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ]:[
                    { prop: 'pid', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',width:"100px"},
                    { prop: 'productName', label: '商品名',sortable:'custom',width:"160px"},
                    { prop: 'property', label: '申报要素',sortable:'custom',width:"300px",custom:this.dbClick},
                    { prop: 'numWeight', label: '数重比',sortable:'custom',width:"90px"},
                    { prop: 'faOneUnit', label: '第一法定单位',sortable:'custom',width:"120px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'faTwoUnit', label: '第二单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'cjUnit', label: '成交单位',sortable:'custom',width:"100px",formatData: this.FILTER.filterCjUnit},
                    { prop: 'oneKgNum', label: '每千克成交量',sortable:'custom',width:"120px"},
                    { prop: 'avgPrice', label: '平均单价',sortable:'custom',width:"100px"},
                    { prop: 'avgWeight', label: '平均净重',sortable:'custom',width:"100px"},
                    { prop: 'passOrder', label: '涉通关单',sortable:'custom',width:"100px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'rate', label: '涉税',sortable:'custom',width:"70px",formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }},
                    { prop: 'usageCounter', label: '使用次数',sortable:'custom',width:"100px"},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'plus', clickFun: this.handleAdd },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ];
            },
            handleSizeChange(val) {
                this.params.size = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            handleSizeChange1(val) {
                this.params1.size = val;
                this.searchList();
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange1(val) {
                this.params1.page = val;
                this.searchList();
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            dbClick(row){
                let self = this,_property;
                if(row.property!=''&&row.property!=null){
                    _property = row.property.split('|').map((item)=>{
                        return item.split('=');
                    });
                }else{
                    _property=[]
                }
                self.dialogVisible = true;
                self.product = row;
                let url = '/trade/tradeproductinfo/info/' + row.pid;
                self.$axios.get(url).then((res) => {
                    self.property = res.productProperty;
                    let len = self.property.length,_len = _property.length;
                    for(let i=0;i<len;i++){
                        for(let j=0;j<_len;j++){
                            if(self.property[i].propertyNo==parseInt(_property[j][0])){
                                self.$set(self.property[i],'productValue',_property[j][1]);
                                break
                            }else{

                                self.$set(self.property[i],'productValue','');
                            }
                        }
                    }
                });
            },
            dialogStatus(val){
                this.dialogVisible = val;
            },
            cancelDialog(){
                this.dialogVisible = false;
            },
            enterDialog(){
                let self = this,len = self.property.length,property='';
                if(len==0){
                    self.dialogVisible = false;
                    return false;
                }
                for(let i=0;i< len;i++){
                    if(!self.property[i].productValue&&self.property[i].required==1){
                        let msg = '请填写'+self.property[i].productName;
                        self.$message({
                            type: 'error',
                            message: msg
                        });
                        self.$refs.propertyKey[i].$el.querySelector('input').focus();
                        return false
                    }else{
                        if(i==len-1){
                            property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue:'';
                        }else{
                            property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue+'|':'';
                        }
                    }
                }
                let params = {
                    "pid": self.product.pid,
                    "property": property
                }
                self.$axios.post('/trade/tradeproductinfo/updateProperty',params).then((res)=>{
                    if(res.code==0){
                        self.$message({
                            type: 'success',
                            message: '保存成功'
                        });
                        self.dialogVisible = false;
                        self.getList();
                    }else{
                        self.dialogVisible = false;
                    }
                });
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/oftenProduct/edit',query:{id:row.pid}})
            },
            handleAdd(row){
                let self = this;
                self.$confirm('是否确认添加到默认商品?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    let url = '/trade/tradeproductinfo/setProductDefault/'+row.pid;
                    self.$axios.get(url).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '添加成功'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            handleDelete(row){
                let self = this;
                self.$confirm('是否确认删除商品?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/tradeproductinfo/delete',[row.pid]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch,
            vTable,
            vDialog
        }
    }
</script>
