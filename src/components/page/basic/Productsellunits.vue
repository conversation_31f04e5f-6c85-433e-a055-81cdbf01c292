<template>
    <div>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    roleName:'',
                    sidx:'',
                    order:'',
                },
                rowKey:'spId',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { prop: 'spId', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'isDefault', label: '默认',sortable:'custom',formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; } ,width:"70px"},
                    { prop: 'spName', label: '生产销售单位名称',sortable:'custom',minWidth:"150px"},
                    { prop: 'customsCode', label: '海关十位代码',sortable:'custom',minWidth:"120px"},
                    { prop: 'organCode', label: '组织机构代码',sortable:'custom',minWidth:"120px"},
                    { prop: 'unifyCode', label: '统一社会信用代码',sortable:'custom',minWidth:"150px"},
                    { prop: 'coTenCode', label: '商检十位码',sortable:'custom',minWidth:"120px"},
                    { prop: 'principal', label: '法人姓名',sortable:'custom',width:"100px"},
                    { prop: 'mobile', label: '电话',sortable:'custom',width:"100px"},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { prop: 'spId', label: '序号',sortable:'custom', fixed: 'left'},

                    { prop: 'isDefault', label: '默认',sortable:'custom',formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; } },
                    { prop: 'spName', label: '生产销售单位名称',sortable:'custom'},
                    { prop: 'customsCode', label: '海关十位代码',sortable:'custom'},
                    { prop: 'organCode', label: '组织机构代码',sortable:'custom'},
                    { prop: 'unifyCode', label: '统一社会信用代码',sortable:'custom'},
                    { prop: 'coTenCode', label: '商检十位码',sortable:'custom'},
                    { prop: 'principal', label: '法人姓名',sortable:'custom'},
                    { prop: 'mobile', label: '电话',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/trade/productsellunits/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/productsellunits/edit',query:{id:row.spId}})
            },
            handleDelete(row){
                let self = this;
                self.$confirm('是否确认删除该生产销售单位?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/productsellunits/delete',[row.spId]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vTable
        }
    }
</script>

