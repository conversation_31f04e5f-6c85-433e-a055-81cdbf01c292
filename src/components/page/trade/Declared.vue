<template>
    <div class="white-bg">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
            <div class="button-bar cl">
                <el-button class="fl" type="primary" v-if="ruleForm.createAccess==1&&ruleForm.orderStatus>=600" @click="downloadFile(pdfList)">下载附件</el-button>

                <el-button class="fr" @click="back()">返回</el-button>
                <el-button class="fl" type="primary" @click="frozen" v-if="ruleForm.orderStatus==100||ruleForm.orderStatus==2000">{{ruleForm.frozen==1?'解冻':'冻结'}}</el-button>
            </div>
            <v-block class="mt_1" :blockVisible="block1" title="随附单据">
                <template slot="content">
                    <template v-if="pdfList.length>0">
                        <a class="download-file" @click="downloadFile(item)" v-for="(item,index) in pdfList"><i class="el-icon-document ml_1 mr_1"></i>{{item.substring(item.lastIndexOf('/')+1)}}</a>
                    </template>
                    <p v-else>暂无附件</p>
                </template>
            </v-block>
            <v-block class="mt_1" :blockVisible="block5" title="退回备注" v-if="ruleForm.logList&&ruleForm.logList.length>0">
                <template slot="content">
                    <el-table border style="width: 100%" :data="ruleForm.logList">
                        <el-table-column type="index" align="center" label="序号" width="70"></el-table-column>
                        <el-table-column prop="remark" align="center" label="审核意见" min-width="400"></el-table-column>
                        <el-table-column prop="operUserName" align="center" label="退回人" min-width="200"></el-table-column>
                        <el-table-column prop="operTime" align="center" label="退回时间" min-width="200"></el-table-column>
                    </el-table>
                </template>
            </v-block>
            <v-block class="mt_1" :blockVisible="block2" title="报关基础信息">
                <template slot="content">
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <el-form-item label="单据编号:">
                                {{ruleForm.orderNum}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="单据日期:">
                                {{ruleForm.orderTime}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="外贸公司:" v-if="ruleForm.belongSys==1">
                                {{ruleForm.myName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <el-form-item label="监管方式:">
                                {{ruleForm.supMode}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="征免性质:">
                                {{ruleForm.exemption}}
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="货运公司:">
                                {{ruleForm.hyName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="合同协议号:">
                                {{ruleForm.contractNo}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="运输方式:">
                                {{ruleForm.modeTranName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <el-form-item label="境内收发货人:">
                                {{ruleForm.abName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="境内收发货人代码:">
                                {{ruleForm.abCode}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="抵运国(地区):">
                                {{ruleForm.destinCountryName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="生产销售单位:">
                                {{ruleForm.spName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="生产销售单位代码:">
                                {{ruleForm.spCode}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="指运港:">
                                {{ruleForm.portDestin}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="境外收发货人:">
                                {{ruleForm.overseasCons}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="境外收发货人代码:">
                                {{ruleForm.overseasCode}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="贸易国别(地区):">
                                {{ruleForm.myStateName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="成交方式:">
                                {{ruleForm.cjTypeName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item style="margin-bottom: 0" label="运费:">
                                <el-row :gutter="10" style="margin-left: 0;margin-right: 0;">
                                    <el-col :span="8" style="padding-left: 0">
                                        <el-form-item label-width="0">
                                            {{ruleForm.freightOne | freightOne | filterLabel}}
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label-width="0">
                                            {{ruleForm.freightTwo}}
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label-width="0">
                                            {{ruleForm.freightThree | moneyUnit}}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item style="margin-bottom: 0" label="保险费:">
                                <el-row :gutter="10" style="margin-left: 0;margin-right: 0;">
                                    <el-col :span="8" style="padding-left: 0">
                                        <el-form-item label-width="0">
                                            {{ruleForm.premiumOne | freightOne | filterLabel}}
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label-width="0">
                                            {{ruleForm.premiumTwo}}
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label-width="0">
                                            {{ruleForm.premiumThree | moneyUnit}}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="开票工厂:">
                                {{ruleForm.makeFactory}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="包装种类:">
                                {{ruleForm.packageTypeName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <el-form-item label="申报海关:">
                                {{ruleForm.declareCustomsName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出境关别:">
                                {{ruleForm.deparTypeName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="总件数:">
                                {{ruleForm.topSumNum}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="船名:">
                                {{ruleForm.boatName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="航次:">
                                {{ruleForm.voyage}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="总毛重:">
                                {{ruleForm.topRoughWeight}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="提单号:">
                                {{ruleForm.billNo}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="截关日期:">
                                {{ruleForm.closingDate}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="总净重:">
                                {{ruleForm.topSuttle}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <el-form-item label="箱号:">
                                {{ruleForm.boxName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="柜型:">
                                {{ruleForm.cabinetTypeName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="总金额:">
                                {{ruleForm.topSumPrice}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">

                        <el-col :span="8">
                            <el-form-item label="拼箱:">
                                {{ruleForm.pinBoxName}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="离境口岸:">
                                {{ruleForm.portDeparture}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="体积:">
                                {{ruleForm.topVolume}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="备案号:">
                                {{ruleForm.putRecordsNo}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="许可证号:">
                                {{ruleForm.licenseKey}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="备注:">
                                {{ruleForm.remark}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                </template>
            </v-block>
            <v-block class="mt_1" :blockVisible="block3" title="随附单证">
                <template slot="content">
                    <el-table :data="ruleForm.accessoryList" border style="width: 100%">
                        <el-table-column align="center" label="序号" type="index">
                        </el-table-column>
                        <el-table-column align="center" label="随附单证代码" prop="accessoryName">
                        </el-table-column>
                        <el-table-column align="center" prop="accessoryNumber" label="随附单证编号">
                        </el-table-column>
                    </el-table>
                </template>
            </v-block>
            <v-block class="mt_1" :blockVisible="block4" title="原始货物清单">
                <template slot="content">
                    <el-table class="h-table" border row-key="index" :data="ruleForm.productDetail" style="width: 100%">
                        <el-table-column align="center" type="index" fixed label="序号" width="50"></el-table-column>
                        <el-table-column align="center" prop="productName" label="品名" min-width="200">
                        </el-table-column>
                        <el-table-column align="center" prop="hsCode" label="HSCODE" min-width="100">
                        </el-table-column>
                        <el-table-column align="center" prop="property" label="规格" min-width="200">
                        </el-table-column>
                        <el-table-column align="center" prop="numberPackages" label="件数" min-width="80">
                        </el-table-column>
                        <el-table-column align="center" prop="roughWeight" label="毛重KG" min-width="80">
                        </el-table-column>
                        <el-table-column align="center" prop="suttle" label="净重KG" min-width="80">
                        </el-table-column>
                        <el-table-column align="center" prop="volumeTrade" label="成交数量" min-width="100">
                        </el-table-column>
                        <el-table-column align="center" prop="cjUnitId" label="成交单位" min-width="100">
                            <template slot-scope="scope">
                                {{scope.row.cjUnitId|filterCjUnit}}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="unitPrice" label="单价" min-width="80">
                        </el-table-column>
                        <el-table-column align="center" prop="currencyId" label="币种" min-width="100">
                            <template slot-scope="scope">
                                {{scope.row.currencyId|moneyUnit}}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="totalPrices" label="总价" min-width="80">
                        </el-table-column>
                        <el-table-column align="center" prop="churProAddId" label="境内货源地" min-width="200">
                            <template slot-scope="scope">
                                {{scope.row.churProAddId|filterChurProAdd}}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="faOneNum" label="法定数量" min-width="100">
                        </el-table-column>
                        <el-table-column align="center" prop="faOneUnit" label="法定单位" min-width="100">
                        </el-table-column>
                        <el-table-column align="center" prop="faTwoNum" label="第二数量" min-width="100">
                        </el-table-column>
                        <el-table-column align="center" prop="faTwoUnit" label="第二单位" min-width="100">
                        </el-table-column>
                    </el-table>
                    <el-row :gutter="10" class="computed-count mt_1">
                        <el-col :span="8">
                            <el-form-item label="总件数:">{{ruleForm.downSumNum}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="总金额:">{{ruleForm.downSumPrice}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="总毛重:">{{ruleForm.downRoughWeight}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="汇总件数:">{{ruleForm.downHuiSumNum}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="汇总金额:">{{ruleForm.downHuiSumPrice}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="汇总毛重:">{{ruleForm.downHuiRoughWeight}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="件数差:">{{ruleForm.downDiffer}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="汇总净重:">{{ruleForm.downSuttle}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="毛重差:">{{ruleForm.downRoughWeightDiffer}}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="单位净重金额:">{{ruleForm.downUnitSuttlePrice}}</el-form-item>
                        </el-col>
                    </el-row>
                </template>
            </v-block>
        </el-form>
    </div>
</template>
<script>
    import vBlock from './../public/Block';
    export default {
        data: function () {
            return {
                block1: true,
                block2: true,
                block3: true,
                block4: true,
                block5: true,
                pdfList:[],
                ruleForm: {},
                rules: {},
            }
        },
        mounted(){
            this.ruleForm.orderId = this.$route.query.id;
            this.getDetail();
        },
        methods:{
            getDetail(){
                let self = this,url = '/trade/comreceiptorder/info/'+self.ruleForm.orderId;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        self.ruleForm = self.$util.extend(res.comReceiptOrder);
                        self.ruleForm.excelUrl = res.comReceiptOrder.excelUrl?res.comReceiptOrder.excelUrl.split(','):[];
                        self.getPdf();
                    }
                });
            },
            downloadFile(url){
                this.$util.downloadFile(url);
            },
            getPdf(){
                let self = this,url='/trade/comreceiptcreatepdf/getPdfList/'+self.ruleForm.orderId;
                self.pdfList = [];
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        res.orderList.length>0?self.ruleForm.createAccess =1:self.ruleForm.createAccess =0;
                        self.pdfList = res.orderList.map(item=>{
                            return item.accUrl;
                        });
                    }
                });
            },
            //冻结解冻订单
            frozen(){
                let self = this;
                let str = self.ruleForm.frozen==1?'是否确认解冻订单':'是否确认冻结订单';//是否冻结0否1是
                let frozen = self.ruleForm.frozen==1?'0':'1';
                self.$prompt(str, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /\S/,
                    inputErrorMessage: '请输入备注'
                }).then(({ value }) => {
                    self.$axios.get('/trade/comreceiptorder/frozenConfirm',{
                        params:{
                            orderId:self.ruleForm.orderId,
                            frozen:frozen,
                            remark:value
                        }
                    }).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },
            //预配确认
            preplanConfirm(){
                let self = this,url='/trade/comreceiptorder/preplanConfirm/'+self.ruleForm.orderId;
                self.$confirm('是否确认已有预配', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(() => {
                    self.$axios.get(url).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },
            back(){
                this.$router.go(-1);
            }
        },
        components:{
            vBlock
        }
    }
</script>
<style lang="less" scoped>
    .el-tabs__header{margin: 0;}
    .el-form-item{margin-bottom: 0!important;}
    .h-table{
    tr td{padding: 0;}
    .el-input__inner{padding: 0 5px;border: none}
    .cell,th div{padding: 0;}
    }
    .computed-count{
    .el-form-item{margin-bottom: 0}
    }
</style>
