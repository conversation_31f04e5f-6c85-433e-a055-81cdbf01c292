<template>
    <div>
        <v-search labelWidth="100px" :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="单据编号">
                        <el-input v-model="params.orderNum" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="合同协议号">
                        <el-input v-model="params.contractNo" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="单据日期">
                        <el-date-picker v-model="params.time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </template>
            <template slot="form2">
                <el-col :span="6">
                    <el-form-item label="申报状态">
                        <el-select v-model="params.orderStatus" placeholder="请选择">
                            <el-option v-for="item in orderStatus" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="货运公司">
                        <el-select v-model="params.hyId" placeholder="请选择">
                            <el-option v-for="item in hyList" :key="item.hyId" :label="item.hyName" :value="item.hyId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="境内收发货人">
                        <el-select v-model="params.abId" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="getAbList" :loading="loading">
                            <el-option v-for="item in abList"
                                :key="item.abId"
                                :label="item.abName"
                                :value="item.abId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
<!--                <el-button type="primary" @click="handleAdd('1')">(简易模式)新增</el-button>-->
<!--                <el-button type="primary" @click="handleAdd('2')">(专家模式)新增</el-button>-->
                <el-button type="primary" @click="handleAdd('2')">新增</el-button>
            </el-row>
            <el-tabs type="card" v-model="params.orderStatus" @tab-click="getList">
                <el-tab-pane :label="'待发送('+count.tradeWaitSend+')'" name="0"></el-tab-pane>
                <el-tab-pane :label="'已发送('+count.tradeSend+')'" name="100"></el-tab-pane>
                <el-tab-pane :label="'待修改('+count.tradeWaitUpdate+')'" name="50"></el-tab-pane>
                <el-tab-pane :label="'待申报('+count.waitDeclaration+')'" name="600"></el-tab-pane>
                <el-tab-pane :label="'待确认('+count.confirmed+')'" name="500"></el-tab-pane>
                <el-tab-pane :label="'已申报('+count.declared+')'" name="630"></el-tab-pane>
                <el-tab-pane :label="'待查验('+count.waitCheck+')'" name="800"></el-tab-pane>
                <el-tab-pane label="全部" name="10000"></el-tab-pane>
<!--                <el-tab-pane label="我的" name=" "></el-tab-pane>-->
            </el-tabs>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';
    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    orderNum:'',
                    contractNo:'',
                    time:[
                        new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate()),
                        new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate())
                    ],
                    orderStatus:'0',
                    hyId:'',
                    abId:'',
                },
                count:{
                    tradeWaitSend:0,
                    tradeSend:0,
                    tradeWaitUpdate:0,
                    waitDeclaration:0,
                    declared:0,
                    waitCheck:0,
                    confirmed:0,
                },
                    time:[
                        new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate()),
                        new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate())
                    ],
                rowKey:'orderId',
                orderStatus:this.STATUS.tradeOrderStatus,
                hyList:[],
                abList:[],
                loading:'',
                total:0,
                totalPage:1,
                tableData: [{orderStatus:''}],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'orderStatus',fixed: 'left', label: '单据状态',sortable:'custom',minWidth:'140px',formatData: this.FILTER.filterStatus},
                    { prop: 'orderNum', label: '单据编号',sortable:'custom',minWidth:'140px'},
                    { prop: 'orderTime', label: '单据日期',sortable:'custom',minWidth:'140px'},
                    { prop: 'contractNo', label: '合同协议号',sortable:'custom',minWidth:'160px'},
                    { prop: 'orderType', label: '贸易方式',sortable:'custom',minWidth:'140px',formatData: function(val) { return val == 1 ? '一般贸易' :val == 2 ? '市场采购' : '未知';}},
                    { prop: 'abName', label: '境内收发货人',sortable:'custom',minWidth:'180px'},
                    { prop: 'declareCustomsName', label: '申报海关',sortable:'custom',minWidth:'140px'},
                    { prop: 'declareNo', label: '报关单号',sortable:'custom',minWidth:'140px',url:'https://swapp.singlewindow.cn/decserver/entries/ftl/1/0/0/'},
                    { prop: 'trOrderNum', label: '转关单号',sortable:'custom',minWidth:'140px',url:'https://swapp.singlewindow.cn/decserver//transit/cancel/transferCheck/'},
                    { prop: 'billNo', label: '提运单号',sortable:'custom',minWidth:'140px'},
                    { prop: 'boxName', label: '箱号',sortable:'custom',minWidth:'70px'},
                    { prop: 'downSumNum', label: '总件数',sortable:'custom',minWidth:'100px'},
                    { prop: 'downRoughWeight', label: '总毛重',sortable:'custom',minWidth:'100px'},
                    { prop: 'downSumPrice', label: '总金额',sortable:'custom',minWidth:'100px'},
                    { prop: 'boatName', label: '航名',sortable:'custom',minWidth:'70px'},
                    { prop: 'voyage', label: '航次',sortable:'custom',minWidth:'70px'},
                    { prop: 'destinCountryName', label: '抵运国(地区)',sortable:'custom',minWidth:'160px'},
                    { prop: 'applyTypeName', label: '申报方式',sortable:'custom',minWidth:'140px'},
                    { prop: 'deparTypeName', label: '出境关别',sortable:'custom',minWidth:'140px'},
                    { prop: 'hyName', label: '货运公司',sortable:'custom',minWidth:'140px'},
                    { prop: 'myName', label: '外贸公司',sortable:'custom',minWidth:'140px'},
                    { prop: 'remark', label: '备注',sortable:'custom',minWidth:'140px'},
                    { prop: 'updateUserName', label: '修改人',sortable:'custom',minWidth:'140px'},
                    { prop: 'updateUserTime', label: '修改时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'commitUserName', label: '提交人',sortable:'custom',minWidth:'140px'},
                    { prop: 'commitUserTime', label: '提交时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'auditorName', label: '审核人',sortable:'custom',minWidth:'140px'},
                    { prop: 'auditorTime', label: '审核时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'declareUserName', label: '申报人',sortable:'custom',minWidth:'140px'},
                    { prop: 'declareUserTime', label: '申报时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'oper', label: '操作', fixed: 'right',minWidth:'120px',
                        oper: [
                            { class: 'edit',statusKey:'orderStatus',statusVal:[0,50],clickFun: this.handleEdit,content:'编辑'},
                            { class: 'zoom-in',statusKey:'orderStatus',statusVal:[100,200,250,300,400,450,470,500,550,600,630,650,700,730,750,770,800,850,900,950,970,1000,1200,2000,5000], clickFun: this.handleDetail ,content:'查看详情'},
                            { class: 'lock',statusKey:'orderStatus',statusVal:[100], clickFun: this.frozen,content:'冻结' },//冻结
                            { class: 'unlock',statusKey:'orderStatus',statusVal:[2000], clickFun: this.unFrozen,content:'解冻' },//冻结
                            { class: 'document-delete',statusKey:'orderStatus',statusVal:[50], clickFun: this.destroy,content:'作废' },//作废
                            { class: 'delete',statusKey:'orderStatus',statusVal:[0], clickFun: this.handleDelete,content:'删除'},
                        ]
                    },
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'orderStatus',fixed: 'left', label: '单据状态',sortable:'custom',minWidth:'140px',formatData: this.FILTER.filterStatus},
                    { prop: 'orderNum', label: '单据编号',sortable:'custom',minWidth:'140px'},
                    { prop: 'orderTime', label: '单据日期',sortable:'custom',minWidth:'140px'},
                    { prop: 'contractNo', label: '合同协议号',sortable:'custom',minWidth:'160px'},
                    { prop: 'orderType', label: '贸易方式',sortable:'custom',minWidth:'140px',formatData: function(val) { return val == 1 ? '一般贸易' :val == 2 ? '市场采购' : '未知';}},
                    { prop: 'abName', label: '境内收发货人',sortable:'custom',minWidth:'180px'},
                    { prop: 'declareCustomsName', label: '申报海关',sortable:'custom',minWidth:'140px'},
                    { prop: 'declareNo', label: '报关单号',sortable:'custom',minWidth:'140px',url:'https://swapp.singlewindow.cn/decserver/entries/ftl/1/0/0/'},
                    { prop: 'trOrderNum', label: '转关单号',sortable:'custom',minWidth:'140px',url:'https://swapp.singlewindow.cn/decserver//transit/cancel/transferCheck/'},
                    { prop: 'billNo', label: '提运单号',sortable:'custom',minWidth:'140px'},
                    { prop: 'boxName', label: '箱号',sortable:'custom',minWidth:'70px'},
                    { prop: 'downSumNum', label: '总件数',sortable:'custom',minWidth:'100px'},
                    { prop: 'downRoughWeight', label: '总毛重',sortable:'custom',minWidth:'100px'},
                    { prop: 'downSumPrice', label: '总金额',sortable:'custom',minWidth:'100px'},
                    { prop: 'boatName', label: '航名',sortable:'custom',minWidth:'70px'},
                    { prop: 'voyage', label: '航次',sortable:'custom',minWidth:'70px'},
                    { prop: 'destinCountryName', label: '抵运国(地区)',sortable:'custom',minWidth:'160px'},
                    { prop: 'applyTypeName', label: '申报方式',sortable:'custom',minWidth:'140px'},
                    { prop: 'deparTypeName', label: '出境关别',sortable:'custom',minWidth:'140px'},
                    { prop: 'hyName', label: '货运公司',sortable:'custom',minWidth:'140px'},
                    { prop: 'myName', label: '外贸公司',sortable:'custom',minWidth:'140px'},
                    { prop: 'remark', label: '备注',sortable:'custom',minWidth:'140px'},
                    { prop: 'updateUserName', label: '修改人',sortable:'custom',minWidth:'140px'},
                    { prop: 'updateUserTime', label: '修改时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'commitUserName', label: '提交人',sortable:'custom',minWidth:'140px'},
                    { prop: 'commitUserTime', label: '提交时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'auditorName', label: '审核人',sortable:'custom',minWidth:'140px'},
                    { prop: 'auditorTime', label: '审核时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'declareUserName', label: '申报人',sortable:'custom',minWidth:'140px'},
                    { prop: 'declareUserTime', label: '申报时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'oper', label: '操作', fixed: 'right',minWidth:'120px',
                        oper: [
                            { class: 'edit',statusKey:'orderStatus',statusVal:[0,50],clickFun: this.handleEdit,content:'编辑'},
                            { class: 'zoom-in',statusKey:'orderStatus',statusVal:[100,200,250,300,400,450,470,500,550,600,630,650,700,730,750,770,800,850,900,950,970,1000,1200,2000,5000], clickFun: this.handleDetail ,content:'查看详情'},
                            { class: 'lock',statusKey:'orderStatus',statusVal:[100], clickFun: this.frozen,content:'冻结' },//冻结
                            { class: 'unlock',statusKey:'orderStatus',statusVal:[2000], clickFun: this.unFrozen,content:'解冻' },//冻结
                            { class: 'document-delete',statusKey:'orderStatus',statusVal:[50], clickFun: this.destroy,content:'作废' },//作废
                            { class: 'delete',statusKey:'orderStatus',statusVal:[0], clickFun: this.handleDelete,content:'删除'},
                        ]
                    },
                ],
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
            this.getHyList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                let params = this.$util.extend(self.params);
                try {
                    params.startTime = params.time[0]?params.time[0]:'';
                    params.endTime = params.time[1]?params.time[1]:'';
                }catch (e) {

                }
                delete params.time;
                let url = '/trade/comreceiptorder/list'
                if(self.params.orderStatus==10000)params.orderStatus='';
                if(self.params.orderStatus==' '){
                    url='/trade/comreceiptorder/myBindinglist'
                }
                self.$axios.get(url,{params: params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                    self.getCount();
                });
            },
            getCount(){
                let self = this;
                self.$axios.get('/trade/comreceiptorder/count').then((res) => {
                    if(res.code==0){
                        self.count=res;
                    }
                });

            },
            getHyList(){
                let self = this;
                self.$axios.get('/trade/comreceiptorder/hyList',{headers: {'closeLoading': true}}).then((res) => {
                    if(res.code==0) self.hyList = res.freight;
                });
            },
            getAbList(query){
                let self = this;
                self.loading = true;
                self.$axios.get('/trade/abroadconinfo/list',{
                    params:{username: query, page: 1, limit: 50},
                    headers: {'closeLoading': true}
                }).then((res) => {
                    self.loading = false;
                    if(res.code==0) self.abList = res.page.list;
                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleAdd(pattern){
                let path;
                if(pattern==1){
                    path = '/home/<USER>/simple';
                }else if(pattern==2){
                    path = '/home/<USER>/expert';
                }else{
                    path = '/home/<USER>/simple';
                }
                this.$router.push({path: path})
            },
            handleEdit(row){
                let self = this,path,pattern=row.orderPattern,orderStatus=row.orderStatus;  //1简易模式,2专家模式
                // if(row.designationUser==0){
                //     self.$axios.post('/trade/comreceiptorder/bindingOrder',[row.orderId]).then((res) => {
                //         if(res.code==0){
                //             path = pattern==1?'/home/<USER>/simple':'/home/<USER>/expert'
                //             this.$router.push({path: path,query:{id:row.orderId}});
                //         }
                //     });
                // }else{
                //     path = pattern==1?'/home/<USER>/simple':'/home/<USER>/expert'
                //     this.$router.push({path: path,query:{id:row.orderId}});
                // }
                path = pattern==1?'/home/<USER>/simple':'/home/<USER>/expert'
                this.$router.push({path: path,query:{id:row.orderId}});
            },
            handleDetail(row){
                let path,pattern=row.orderPattern,orderStatus=row.orderStatus;  //1简易模式,2专家模式
                if(orderStatus==500){
                    path = '/home/<USER>/confirm'
                }else if(orderStatus<=600||orderStatus==2000){
                    path = '/home/<USER>/declared';
                }else{
                    path='/home/<USER>/detail';
                }
                this.$router.push({path: path,query:{id:row.orderId}});
            },
            handleDelete(row){
                let self = this;
                self.$confirm('是否确认删除该单据?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/comreceiptorder/delete',[row.orderId]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            destroy(row){
                this.$confirm('确认作废单据吗', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(({ value }) => {
                    this.$axios.get('/trade/comreceiptorder/chStatus',{params:{orderId:row.orderId,orderStatus:550}}).then((res) => {
                        if(res.code==0){
                            this.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            this.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            //冻结
            frozen(row){
                this.handleFrozen(row,1)
            },
            //解冻
            unFrozen(row){
                this.handleFrozen(row,0)
            },
            handleFrozen(row,frozen){
                let str = frozen==1?'是否确认冻结订单':'是否确认解冻订单';//是否冻结0否1是
                this.$prompt(str, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /\S/,
                    inputErrorMessage: '请输入备注'
                }).then(({ value }) => {
                    this.$axios.get('/trade/comreceiptorder/frozenConfirm',{
                        params:{
                            orderId:row.orderId,
                            frozen:frozen,
                            remark:value
                        }
                    }).then((res) => {
                        if(res.code==0){
                            this.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            this.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

