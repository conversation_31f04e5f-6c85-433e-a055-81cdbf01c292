<template>
    <div class="white-bg">
        <div class="button-bar needfix cl" :class="{'isCollapse':isCollapse}">
<!--            <el-button class="fl" type="primary" @click="freeOrder" v-if="ruleForm.orderId&&ruleForm.designationUser!=0">释放</el-button>-->
            <el-button class="fr" @click="back()">返回</el-button>
            <el-button class="fr" type="primary" @click="submitForm('ruleForm','2')">保存并发送</el-button>
            <el-button class="fr" type="primary" @click="submitForm('ruleForm','1')">保存</el-button>

        </div>
        <div style="height: 32px"></div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <v-block class="mt_1" :blockVisible="block1" title="报关基础信息">
                    <template slot="content">
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="单据编号:" prop="orderNum">
                                    <el-input disabled v-model="ruleForm.orderNum"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="单据日期:" prop="orderTime">
                                    <el-date-picker v-model="ruleForm.orderTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="外贸公司:" prop="myName">
                                    <el-input v-model="ruleForm.myName" disabled></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <!--<el-form-item label="监管方式:" prop="supId">-->
                                    <!--<v-Select-->
                                        <!--v-model="ruleForm.supId"-->
                                        <!--:options="supMode.options"-->
                                        <!--:params="supMode.params"-->
                                        <!--:searchMethod="searchSupMode"-->
                                        <!--:change="changeSupMode"-->
                                    <!--&gt;</v-Select>-->
                                <!--</el-form-item>-->
                                <el-form-item label="监管方式:">
                                    <el-input disabled v-model="ruleForm.supMode"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <!--<el-form-item label="征免性质:" prop="exemptionId">-->
                                    <!--<v-Select-->
                                        <!--v-model="ruleForm.exemptionId"-->
                                        <!--:options="exemption.options"-->
                                        <!--:params="exemption.params"-->
                                        <!--:searchMethod="searchExemption"-->
                                        <!--:change="changeExemption"-->
                                    <!--&gt;</v-Select>-->
                                <!--</el-form-item>-->
                                <el-form-item label="征免性质:">
                                    <el-input disabled v-model="ruleForm.exemption"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="货运公司:" prop="hyId">
                                    <v-Select
                                        v-model="ruleForm.hyId"
                                        :options="hy.options"
                                        :params="hy.params"
                                        :searchMethod="searchHy"
                                        :change="changeHy"
                                        :getData="getHyList"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="合同协议号:" prop="contractNo">
                                    <el-input v-model="ruleForm.contractNo"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </template>
                </v-block>
                <v-block class="mt_1" :blockVisible="block2" title="原始货物清单">
                    <template slot="content">
                        <el-form-item label-width="0px" prop="excelUrl">
                            <el-upload
                                class="upload-demo simple-upload"
                                action="/aquaman/trade/file/uploads"
                                name="files"
                                :data="{fileType:1,orderNum:ruleForm.orderNum}"
                                :headers="header"
                                :on-preview="handlePreview"
                                :on-remove="handleRemove"
                                :on-progress="handleProgress"
                                :on-error="handleError"
                                :on-success="handleSuccess"
                                :before-remove="beforeRemove"
                                multiple
                                :file-list="ruleForm.excelUrl">
                                <el-button size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip">支持所有格式上传；</div>
                            </el-upload>
                        </el-form-item>
                    </template>
                </v-block>
            </el-form>
        </div>
    </div>
</template>
<script>
    import vBlock from './../public/Block';
    import vSelect from './../public/SearchSelect';
    // import {supMode,exemption} from './../../../util/config';
    let token =  localStorage.getItem('adminToken');
    export default {
        data: function () {
            return {
                ruleForm: {
                    orderId:'',
                    orderType:'1',//贸易方式1一般贸易2市场采购
                    applyTypeId:'1', //申报方式1一体化2非一体化
                    applyTypeName:'一体化',
                    orderPattern:'1',//创建模式1简易模式,2专家模式 ,
                    orderNum:'',
                    orderTime:'',
                    myId:'',
                    myName:'',
                    supId:'0110',
                    supMode:'一般贸易',
                    exemptionId:'101',
                    exemption:'一般征税',
                    hyId:'',
                    hyName:'',
                    contractNo:'',
                    excelUrl:[]
                },
                supMode:{
                    options:supMode,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label',
                    }
                },
                exemption:{
                    options:exemption,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label',
                    }
                },
                hy:{
                    options:[],
                    params:{
                        label:'hyName',
                        value:'hyId',
                        key:'hyId',
                        itemName:'hyName'
                    }
                },
                block1:true,
                block2:true,
                loading:'',
                header: {token: token},
                rules: {
                    orderTime: [
                        { required: true, message: '请选择单据日期', trigger: 'change' }
                    ],
                    myName: [
                        { required: true, message: '未获取公司信息,请重新载入页面', trigger: 'blur' }
                    ],
                    supId: [
                        { required: true, message: '请选择监管方式', trigger: 'blur' }
                    ],
                    exemptionId: [
                        { required: true, message: '请选择征免性质', trigger: 'blur' }
                    ],
                    hyId: [
                        { required: true, message: '请选择货运公司', trigger: 'blur' }
                    ],
                    contractNo: [
                        { required: true, message: '请填写合同协议号', trigger: 'blur' }
                    ],
                    excelUrl: [
                        { required: true, message: '请上传原始货物清单', trigger: 'change' }
                    ]
                },
            }
        },
        computed:{
            isCollapse(){
                return this.$store.state.isCollapse;
            },
        },
        mounted(){
            this.$store.commit('CollapseSidebar',true);//关闭侧边栏
            let myDate = new Date();
            this.ruleForm.orderTime = myDate.getFullYear()+'-'+(myDate.getMonth()+1<10?('0'+(myDate.getMonth()+1)):(myDate.getMonth()+1))+'-'+(myDate.getDate()<10?('0'+myDate.getDate()):myDate.getDate());
            this.getHyList();
            this.getCompany();
            if(this.$route.query.id){
                this.ruleForm.orderId = this.$route.query.id;
                this.getDetail();
            }else{
                this.getOrderNum();
            }
        },
        methods:{
            getDetail(){
                let self = this,url = '/trade/comreceiptorder/info/'+self.ruleForm.orderId;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        self.ruleForm = self.$util.extend(res.comReceiptOrder);
                        if(res.comReceiptOrder.excelUrl){
                            let fileList = res.comReceiptOrder.excelUrl.split(',');
                            self.ruleForm.excelUrl = [];
                            fileList.map((item,index)=>{
                                self.ruleForm.excelUrl.push({name:item.substring(item.lastIndexOf('/')+1),uid:'123213213213'+index,url:item})
                            })
                        }else{
                            self.ruleForm.excelUrl=[];
                        }
                    }
                });
            },
            getCompany(){
                let self = this;
                self.$axios.get('/sys/user/info',{headers: {'closeLoading': true}}).then((res) => {
                    debugger
                    if(res.code==0){
                        self.ruleForm.myId = res.user.companyId
                        self.ruleForm.myName = res.user.companyName
                    }else{
                        self.$message({
                            message: "公司信息获取失败,请重新打开页面",
                            type: 'error'
                        })
                    }
                });
            },
            getOrderNum(){
                return
                let self = this;
                self.$axios.get('/trade/comreceiptorder/getOrderNum',{
                    headers: {'closeLoading': true},
                    params:{
                        orderType:1,  //1一般贸易，2市场采购
                        applyTypeId:1 //申报方式1一体化2非一体化
                    }
                }).then((res) => {
                    if(res.code==0){
                        self.ruleForm.orderNum = res.orderNumber;
                    }else{
                        self.$message({
                            message: "单据编号获取失败,请重新打开页面",
                            type: 'error'
                        });
                    }
                });
            },

            searchSupMode(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeSupMode(vId){
                let obj = {};
                obj = this.supMode.options.find((item)=>{
                    return item.codeValue === vId;//筛选出匹配数据
                });
                this.ruleForm.supMode = obj.codeName;
            },
            searchExemption(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeExemption(vId){
                let obj = {};
                obj = this.exemption.options.find((item)=>{
                    return item.value === vId;//筛选出匹配数据
                });
                this.ruleForm.exemption = obj.label;
            },
            getHyList(search){
                let self = this;
                self.$axios.get('/trade/comreceiptorder/hyList',{headers: {'closeLoading': true},params:{username:search}}).then((res) => {
                    if(res.code==0) self.hy.options = res.freight;
                });
            },
            searchHy(){
                return true;
            },
            changeHy(vId){
                let obj = {};
                obj = this.hy.options.find((item)=>{
                    return item.hyId === vId;//筛选出匹配数据
                });
                this.ruleForm.hyName = obj.hyName;
            },
            handleProgress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '正在上传...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            handleError(){
                this.loading.close();
                this.$message({
                    message: "上传失败,请重试",
                    type: 'error'
                })
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handleSuccess(res, file,fileList) {
                this.loading.close();
                fileList[fileList.length-1].name= res.result.substring(res.result.lastIndexOf('/')+1);
                fileList[fileList.length-1].url= res.result;
                this.ruleForm.excelUrl=fileList;
            },
            handlePreview(file){
                window.location=file.url;
                try{
                    let elemIF = document.createElement("iframe");
                    elemIF.src = file.url;
                    elemIF.style.display = "none";
                    document.body.appendChild(elemIF);
                }catch(e){
                    debugger
                }
            },
            beforeRemove(file, fileList) {
                return this.$confirm(`确定移除 ${ file.name }？`);
            },

            //释放单据
            freeOrder(){
                let self = this;
                self.$confirm('确认释放单据吗', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(({ value }) => {
                    self.$axios.post('/trade/comreceiptorder/freedOrder',[self.ruleForm.orderId]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },

            submitForm(formName,status) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = status==2?'/trade/comreceiptorder/saveAndSend':'/trade/comreceiptorder/save';
                        let params = self.$util.extend(self.ruleForm),excelUrl;
                        excelUrl=params.excelUrl.map((item)=>{
                            return item.response?item.response.result:item.url;
                        });
                        params.excelUrl = excelUrl.join(',');
                        self.$axios.post(url,params).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                if(status==1&&(res.orderId||self.ruleForm.orderId)){
                                    self.$router.replace({path: '/home/<USER>/simple',query:{id:res.orderId}})
                                    if(res.orderId)self.ruleForm.orderId = res.orderId;
                                    self.getDetail();
                                }else{
                                    self.back();
                                }
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$store.commit('CollapseSidebar',false);//打开侧边栏
                this.$router.replace({path: '/home/<USER>/list'});
            }
        },
        components:{
            vBlock,vSelect
        }
    }
</script>
<style lang="less">
    .el-form-item{margin-bottom: 5px!important;}
    .simple-upload{
        .el-upload--text{border: none;width: 100%;height: auto}
    }
</style>
