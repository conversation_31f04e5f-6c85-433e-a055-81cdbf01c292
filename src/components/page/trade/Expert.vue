<template>
    <div class="white-bg">
        <div class="button-bar needfix cl" :class="{'isCollapse':isCollapse}">
<!--            <el-button class="fl" type="primary" @click="freeOrder" v-if="ruleForm.orderId&&ruleForm.designationUser!=0">释放</el-button>-->
            <el-button class="fl" type="primary" @click="openUploadExcel">导入</el-button>
            <a style="margin-left: 10px" class="fl" href="http://114.55.210.26:8084/file/resources/trading.xlsx"><el-button type="primary">模板下载</el-button></a>
            <el-button class="fr" @click="back()">返回</el-button>
            <el-button class="fr" type="success" @click="submitForm('ruleForm',2)">保存并发送</el-button>
            <el-button class="fr" type="primary" @click="submitForm('ruleForm',1)">保存</el-button>
            <el-button class="fr" type="danger" v-if="ruleForm.orderStatus==50" @click="submitStatus('ruleForm','550')">作废</el-button>
        </div>
        <div style="height: 32px"></div>
        <div class="modal-content">
            <v-block class="mt_1" :blockVisible="block1" title="退回备注" v-if="ruleForm.logList&&ruleForm.logList.length>0">
                <template slot="content">
                    <el-table border style="width: 100%" :data="ruleForm.logList">
                        <el-table-column type="index" align="center" label="序号" width="70"></el-table-column>
                        <el-table-column prop="remark" align="center" label="审核意见" min-width="400"></el-table-column>
                        <el-table-column prop="operUserName" align="center" label="退回人" min-width="200"></el-table-column>
                        <el-table-column prop="operTime" align="center" label="退回时间" min-width="200"></el-table-column>
                    </el-table>
                </template>
            </v-block>
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <v-block class="mt_1" :blockVisible="block1" title="报关基础信息" v-enterToNext="true">
                    <template slot="content">
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="单据编号:" prop="orderNum">
                                    <el-input disabled v-model="ruleForm.orderNum"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="监管方式:">
                                    <el-input disabled v-model="ruleForm.supMode"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="单据日期:" prop="orderTime">
                                    <el-date-picker v-model="ruleForm.orderTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">

<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="征免性质:">-->
<!--                                    <el-input disabled v-model="ruleForm.exemption"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                            <el-col :span="8">
                                <el-form-item label="合同协议号:" prop="contractNo">
                                    <el-input v-model="ruleForm.contractNo"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="外贸公司:" prop="myName">
                                    <el-input v-model="ruleForm.myName" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="货运公司:" prop="hyId">
                                    <v-Select
                                        v-model="ruleForm.hyId"
                                        :options="hy.options"
                                        :params="hy.params"
                                        :searchMethod="searchHy"
                                        :change="changeHy"
                                        :getData="getHyList"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
<!--                        </el-row>-->
<!--                        <el-row :gutter="10">-->

<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="运输方式:" prop="modeTranId">-->
<!--                                    <v-Select-->
<!--                                        v-model="ruleForm.modeTranId"-->
<!--                                        :options="modeTran.options"-->
<!--                                        :params="modeTran.params"-->
<!--                                        :searchMethod="searchModeTran"-->
<!--                                        :change="changeModeTran"-->
<!--                                    ></v-Select>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="境内收发货人:" prop="abId">
                                    <v-Select
                                        v-model="ruleForm.abId"
                                        :options="ab.options"
                                        :params="ab.params"
                                        :searchMethod="searchAb"
                                        :change="changeAb"
                                        :getData="getAbList"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="境内收发货人代码:" prop="abCode">
                                    <el-input disabled v-model="ruleForm.abCode"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="生产销售单位:" prop="spId">
                                    <v-Select
                                        v-model="ruleForm.spId"
                                        :options="sp.options"
                                        :params="sp.params"
                                        :searchMethod="searchSp"
                                        :change="changeSp"
                                        :getData="getSpList"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="生产销售单位代码:" prop="spCode">
                                    <el-input disabled v-model="ruleForm.spCode"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="境外收发货人:" prop="overseasCons">
                                    <el-input v-model="ruleForm.overseasCons"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="运抵国(地区):" prop="destinCountryId">
                                    <v-Select
                                        v-model="ruleForm.destinCountryId"
                                        ref="destinCountry"
                                        :options="destinCountry.options"
                                        :params="destinCountry.params"
                                        :searchMethod="searchDestinCountry"
                                        :change="changeDestinCountry"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="指运港:" prop="portDestinId">
                                    <v-Select
                                        v-model="ruleForm.portDestinId"
                                        :options="portDestin.options"
                                        :params="portDestin.params"
                                        :searchMethod="searchPortDestin"
                                        :change="changePortDestin"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>

<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="境外收发货人代码:" prop="overseasCode">-->
<!--                                    <el-input v-model="ruleForm.overseasCode"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                            <el-col :span="8">
                                <el-form-item label="贸易国别(地区):" prop="myStateId">
                                    <v-Select
                                        v-model="ruleForm.myStateId"
                                        :options="myState.options"
                                        :params="myState.params"
                                        :searchMethod="searchMyState"
                                        :change="changeMyState"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="成交方式:" prop="cjTypeId">
                                    <v-Select
                                        v-model="ruleForm.cjTypeId"
                                        :options="cjType.options"
                                        :params="cjType.params"
                                        :searchMethod="searchCjType"
                                        :change="changeCjType"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item style="margin-bottom: 0" label="运费:" :required="ruleForm.cjTypeId==1||ruleForm.cjTypeId==2">
                                    <el-row :gutter="10" style="margin-left: 0;margin-right: 0;">
                                        <el-col :span="8" style="padding-left: 0">
                                            <el-form-item label-width="0" prop="freightOne" :rules="(ruleForm.cjTypeId==1||ruleForm.cjTypeId==2)?rules.freightOne:[{required: false}]">
                                                <el-select :disabled="ruleForm.cjTypeId==3" filterable default-first-option v-model="ruleForm.freightOne" placeholder="请选择" @change="ruleForm.freightOne==1?ruleForm.freightThree='':'';rateStyle('freightOne','freightTwo')">
                                                    <el-option
                                                        :disabled="ruleForm.cjTypeId==3"
                                                        v-for="(item,index) in feeMarkName"
                                                        :key="item.value+index"
                                                        :label="item.label"
                                                        :value="item.value">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label-width="0" prop="freightTwo" :rules="(ruleForm.cjTypeId==1||ruleForm.cjTypeId==2)?rules.freightTwo:[{required: false}]">
                                                <el-input :disabled="ruleForm.cjTypeId==3" v-model="ruleForm.freightTwo" @change="rateStyle('freightOne','freightTwo')"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label-width="0" prop="freightThree" :rules="(ruleForm.cjTypeId==1||ruleForm.cjTypeId==2)&&ruleForm.freightOne!=1?rules.freightThree:[{required: false}]">
                                                <v-Select
                                                    :disabled="ruleForm.cjTypeId==3||ruleForm.freightOne==1"
                                                    v-model="ruleForm.freightThree"
                                                    :options="unit.options"
                                                    :params="unit.params"
                                                    :searchMethod="searchFreightThree"
                                                ></v-Select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item style="margin-bottom: 0" label="保险费:" :required="ruleForm.cjTypeId==1||ruleForm.cjTypeId==4">
                                    <el-row :gutter="10" style="margin-left: 0;margin-right: 0;">
                                        <el-col :span="8" style="padding-left: 0">
                                            <el-form-item label-width="0" prop="premiumOne" :rules="(ruleForm.cjTypeId==1||ruleForm.cjTypeId==4)?rules.premiumOne:[{required: false}]">
                                                <el-select :disabled="ruleForm.cjTypeId==2||ruleForm.cjTypeId==3" filterable default-first-option v-model="ruleForm.premiumOne" placeholder="请选择" @change="ruleForm.premiumOne==1?ruleForm.premiumThree='':'';rateStyle('premiumOne','premiumTwo')">
                                                    <el-option
                                                        v-for="(item,index) in insurMarkName"
                                                        :key="item.value+index"
                                                        :label="item.label"
                                                        :value="item.value">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label-width="0" prop="premiumTwo" :rules="(ruleForm.cjTypeId==1||ruleForm.cjTypeId==4)?rules.premiumTwo:[{required: false}]">
                                                <el-input :disabled="ruleForm.cjTypeId==2||ruleForm.cjTypeId==3" v-model="ruleForm.premiumTwo" @change="rateStyle('premiumOne','premiumTwo')"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label-width="0" prop="premiumThree" :rules="(ruleForm.cjTypeId==1||ruleForm.cjTypeId==4)&&ruleForm.premiumOne!=1?rules.premiumThree:[{required: false}]">
                                                <v-Select
                                                    :disabled="ruleForm.cjTypeId==2||ruleForm.cjTypeId==3||ruleForm.premiumOne==1"
                                                    v-model="ruleForm.premiumThree"
                                                    :options="unit.options"
                                                    :params="unit.params"
                                                    :searchMethod="searchFreightThree"
                                                ></v-Select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-col>
                        </el-row>
<!--                        <el-row :gutter="10">-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="开票工厂:" prop="makeFactory">-->
<!--                                    <el-input v-model="ruleForm.makeFactory"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                        </el-row>-->
<!--                        <el-row :gutter="10">-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="申报海关:" prop="declareCustomsId">-->
<!--                                    <v-Select-->
<!--                                        v-model="ruleForm.declareCustomsId"-->
<!--                                        :options="declareCustoms.options"-->
<!--                                        :params="declareCustoms.params"-->
<!--                                        :searchMethod="searchDeclareCustoms"-->
<!--                                        :change="changeDeclareCustoms"-->
<!--                                    ></v-Select>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="出境关别:" prop="deparTypeId">-->
<!--                                    <v-Select-->
<!--                                        v-model="ruleForm.deparTypeId"-->
<!--                                        :options="deparType.options"-->
<!--                                        :params="deparType.params"-->
<!--                                        :searchMethod="searchDeparType"-->
<!--                                        :change="changeDeparType"-->
<!--                                    ></v-Select>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="总件数:" prop="topSumNum">-->
<!--                                    <el-input v-model="ruleForm.topSumNum"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                        </el-row>-->
<!--                        <el-row :gutter="10">-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="船名:" prop="boatName">-->
<!--                                    <el-input v-model="ruleForm.boatName"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="航次:" prop="voyage">-->
<!--                                    <el-input v-model="ruleForm.voyage"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="总毛重:" prop="topRoughWeight">-->
<!--                                    <el-input v-model="ruleForm.topRoughWeight"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                        </el-row>-->
<!--                        <el-row :gutter="10">-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="提单号:" prop="billNo">-->
<!--                                    <el-input v-model="ruleForm.billNo"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="截关日期:" prop="closingDate">-->
<!--                                    <el-date-picker v-model="ruleForm.closingDate" type="date"-->
<!--                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd"-->
<!--                                        placeholder="选择日期">-->
<!--                                    </el-date-picker>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="总净重:" prop="topSuttle">-->
<!--                                    <el-input v-model="ruleForm.topSuttle"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                        </el-row>-->
<!--                        <el-row :gutter="10">-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="箱号:" prop="boxName">-->
<!--                                    <div style="display: flex;justify-content: flex-start;align-items: center;flex-wrap: nowrap">-->
<!--                                        <el-input :disabled="boxList.length>1" v-model="ruleForm.boxName"></el-input>-->
<!--                                        <a style="font-size: 24px;cursor: pointer" class="el-icon-circle-plus ml_1" @click="showBoxList"></a>-->
<!--                                    </div>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="柜型:" prop="cabinetTypeId">-->
<!--                                    <el-select filterable default-first-option v-model="ruleForm.cabinetTypeId" multiple :multiple-limit="boxList.length?boxList.length:1" @change="changeRuleFormCabinetType" :disabled="boxList.length>1" placeholder="请选择">-->
<!--                                        <el-option-->
<!--                                            v-for="item in cabinetType.options"-->
<!--                                            :key="item.codeValue"-->
<!--                                            :label="item.label"-->
<!--                                            :value="item.codeValue">-->
<!--                                            &lt;!&ndash;{{item.label}}&ndash;&gt;-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="总金额:" prop="topSumPrice">-->
<!--                                    <el-input v-model="ruleForm.topSumPrice"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                        </el-row>-->
<!--                        <el-row :gutter="10">-->

<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="拼箱:" prop="pinBoxId">-->
<!--                                    <v-Select-->
<!--                                        v-model="ruleForm.pinBoxId"-->
<!--                                        :options="pinBox.options"-->
<!--                                        :params="pinBox.params"-->
<!--                                        :searchMethod="searchPinBox"-->
<!--                                        :change="changePinBox"-->
<!--                                    ></v-Select>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="离境口岸:" prop="portDepartureId">-->
<!--                                    <v-Select-->
<!--                                        v-model="ruleForm.portDepartureId"-->
<!--                                        :options="portDeparture.options"-->
<!--                                        :params="portDeparture.params"-->
<!--                                        :searchMethod="searchPortDeparture"-->
<!--                                        :change="changePortDeparture"-->
<!--                                    ></v-Select>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="体积:" prop="topVolume">-->
<!--                                    <el-input v-model="ruleForm.topVolume"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                        </el-row>-->
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="总件数:" prop="topSumNum">
                                    <el-input v-model="ruleForm.topSumNum" @change="ruleForm.downSumNum = ruleForm.topSumNum"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="总毛重:" prop="topRoughWeight">
                                    <el-input v-model="ruleForm.topRoughWeight" @change="ruleForm.downRoughWeight = ruleForm.topRoughWeight"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="总净重:" prop="topSuttle">
                                    <el-input v-model="ruleForm.topSuttle"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <el-col :span="8">
                                <el-form-item label="包装种类:" prop="packageTypeId">
                                    <v-Select
                                        v-model="ruleForm.packageTypeId"
                                        :options="packageType.options"
                                        :params="packageType.params"
                                        :searchMethod="searchPackageType"
                                        :change="changePackageType"
                                    ></v-Select>
                                </el-form-item>
                            </el-col>
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="总金额:" prop="topSumPrice">-->
<!--                                    <el-input v-model="ruleForm.topSumPrice" @change="ruleForm.downSumPrice = ruleForm.topSumPrice"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="体积:" prop="topVolume">-->
<!--                                    <el-input v-model="ruleForm.topVolume"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="许可证号:" prop="licenseKey">-->
<!--                                    <el-input v-model="ruleForm.licenseKey"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                        </el-row>
                        <el-row :gutter="10">
<!--                            <el-col :span="8">-->
<!--                                <el-form-item label="备案号:" prop="putRecordsNo">-->
<!--                                    <el-input v-model="ruleForm.putRecordsNo"></el-input>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->

                            <el-col :span="24">
                                <el-form-item label="备注:" prop="otherRemark">
                                    <el-input style="max-width: 100%;" placeholder="有特殊要求或其他说明的请在此备注" v-model="ruleForm.otherRemark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </template>
                </v-block>
                <v-block class="mt_1" :blockVisible="block2" title="随附单证">
                    <template slot="content">
                        <el-table :data="accessoryList" border style="width: 100%">
                            <el-table-column align="center" label="序号" type="index">
                            </el-table-column>
                            <el-table-column align="center" label="随附单证代码" prop="accessoryName">
                            </el-table-column>
                            <el-table-column align="center" prop="accessoryNumber" label="随附单证编号">
                            </el-table-column>
                            <el-table-column align="center" label="操作">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="danger" @click="deleteAccessoryList(scope.$index)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="box-list">
                            <el-form :model="accessoryItem" :rules="accessoryRules" ref="accessoryItem" label-width="140px" class="demo-ruleForm box-int">
                                <el-row :gutter="10">
                                    <el-col :span="8">
                                        <el-form-item label="随附单证代码:" prop="accessoryCode">
                                            <v-Select
                                                v-model="accessoryItem.accessoryCode"
                                                :options="accessory.options"
                                                :params="accessory.params"
                                                :searchMethod="searchAccessory"
                                                :change="changeAccessory"
                                            ></v-Select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="随附单证编号:" prop="accessoryNumber">
                                            <el-input @keyup.enter.native="addAccessoryList('accessoryItem')" v-model="accessoryItem.accessoryNumber"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-button type="primary" @click="addAccessoryList('accessoryItem')">确定</el-button>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </template>
                </v-block>
                <v-block class="mt_1" :blockVisible="block3" title="原始货物清单">
                    <template slot="content">
                        <el-table class="h-table" highlight-current-row border row-key="index" :data="ruleForm.productDetail" style="width: 100%">
                            <el-table-column align="center" type="index" fixed label="序号" width="50"></el-table-column>
                            <el-table-column align="center" fixed label="操作" width="90">
                                <template slot-scope="scope">
                                    <a class="h-btn"><i class="el-icon-plus" @click="addProduct($event)"></i></a>
                                    <a class="h-btn"><i class="el-icon-delete" @click="deleteProduct($event,scope.$index)"></i></a>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="品名" min-width="120">
                                <template slot-scope="scope">
                                    <el-input :id="'productName'+scope.$index" @keyup.enter.native="enterProductName(scope.row,scope.$index)" @focus="searchProductName(scope.row)" @input="searchProductName(scope.row)" v-model="ruleForm.productDetail[scope.$index].productName" v-focusAll ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="hsCode" label="HSCODE" min-width="90">
                                <template slot-scope="scope">
                                    <el-input :id="'hsCode'+scope.$index" @keyup.enter.native="enterFocus('hsCode',scope.$index)" @change="searchProduct(scope.$index)" v-model="ruleForm.productDetail[scope.$index].hsCode" v-focusAll></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" show-overflow-tooltip label="申报要素" min-width="200">
                                <template slot-scope="scope">
                                    <span class="property-cont" @dblclick="changeProperty($event,scope.row,scope.$index)">{{scope.row.property}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="numberPackages" label="件数" min-width="50">
                                <template slot-scope="scope">
                                    <el-input @input="ruleForm.productDetail[scope.$index].numberPackages=$util.num(ruleForm.productDetail[scope.$index].numberPackages)" :id="'numberPackages'+scope.$index" @keyup.enter.native="enterFocus('numberPackages',scope.$index)" v-model="ruleForm.productDetail[scope.$index].numberPackages" v-focusAll ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="roughWeight" label="毛重KG" min-width="60">
                                <template slot-scope="scope">
                                    <el-input @input="ruleForm.productDetail[scope.$index].roughWeight=$util.num(ruleForm.productDetail[scope.$index].roughWeight)" :id="'roughWeight'+scope.$index" @keyup.enter.native="enterFocus('roughWeight',scope.$index)" v-model="ruleForm.productDetail[scope.$index].roughWeight" v-focusAll @focus="recordRoughWeight(scope.$index)" @change="changeRoughWeight(scope.$index)"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="suttle" label="净重KG" min-width="60">
                                <template slot-scope="scope">
                                    <el-input @input="ruleForm.productDetail[scope.$index].suttle=$util.num(ruleForm.productDetail[scope.$index].suttle)" :id="'suttle'+scope.$index" @keyup.enter.native="enterFocus('suttle',scope.$index)" v-model="ruleForm.productDetail[scope.$index].suttle" v-focusAll  @focus="recordSuttle(scope.$index)" @change="changeSuttle(scope.$index)"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="volumeTrade" label="成交数量" min-width="70">
                                <template slot-scope="scope">
                                    <el-input @input="ruleForm.productDetail[scope.$index].volumeTrade=$util.num(ruleForm.productDetail[scope.$index].volumeTrade)" :id="'volumeTrade'+scope.$index" @keyup.enter.native="enterFocus('volumeTrade',scope.$index)" v-model="ruleForm.productDetail[scope.$index].volumeTrade" v-focusAll @change="changeVolumeTrade(scope.$index)" @focus="recordVolumeTrade(scope.$index)"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="cjUnitId" label="成交单位" min-width="90">
                                <template slot-scope="scope">
                                    <el-select :id="'cjUnitId'+scope.$index" @keyup.enter.native="enterFocus('cjUnitId',scope.$index)" placeholder="" v-model="ruleForm.productDetail[scope.$index].cjUnitId" filterable default-first-option @change="changeCjUnitId(scope.$index)">
                                        <el-option
                                            v-for="item in cjUnit.options"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="unitPrice" label="单价" min-width="70">
                                <template slot-scope="scope">
                                    <el-input @input="ruleForm.productDetail[scope.$index].unitPrice=$util.num(ruleForm.productDetail[scope.$index].unitPrice)" :id="'unitPrice'+scope.$index" @keyup.enter.native="enterFocus('unitPrice',scope.$index)" v-model="ruleForm.productDetail[scope.$index].unitPrice" v-focusAll @change="changeUnitPrice(scope.$index)"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="currencyId" label="币种" min-width="100">
                                <template slot-scope="scope">
                                    <el-select :id="'currencyId'+scope.$index" @keyup.enter.native="enterFocus('currencyId',scope.$index)" v-model="ruleForm.productDetail[scope.$index].currencyId" filterable default-first-option @change="autoCurrencyId(scope.$index)" placeholder="">
                                        <el-option
                                            v-for="item in unit.options"
                                            :key="item.codeValue"
                                            :label="item.label"
                                            :value="item.codeValue">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="totalPrices" label="总价" min-width="70">
                                <template slot-scope="scope">
                                    <el-input @input="ruleForm.productDetail[scope.$index].totalPrices=$util.num(ruleForm.productDetail[scope.$index].totalPrices)" :id="'totalPrices'+scope.$index" @keyup.enter.native="enterFocus('totalPrices',scope.$index)" v-model="ruleForm.productDetail[scope.$index].totalPrices" v-focusAll @change="changeTotalPrices(scope.$index)"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="境内货源地" min-width="200">
                                <template slot-scope="scope">
                                    <v-Select :id="'churProAddId'+scope.$index" @keyup.enter.native="enterFocus('churProAddId',scope.$index)"
                                              v-model="ruleForm.productDetail[scope.$index].churProAddId"
                                              :options="churProAdd.options" placeholder="" :params="churProAdd.params" :appendBody="true"
                                              :searchMethod="searchChurProAdd" :change="changeChurProAdd"
                                    ></v-Select>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="faOneNum" label="法定数量" min-width="70">
                                <template slot-scope="scope">
                                    <template v-if="ruleForm.productDetail[scope.$index].faOneUnit=='035'">
                                        {{ruleForm.productDetail[scope.$index].faOneNum}}
                                    </template>
                                    <template v-else>
                                        <el-input @input="ruleForm.productDetail[scope.$index].faOneNum=$util.num(ruleForm.productDetail[scope.$index].faOneNum)" :id="'faOneNum'+scope.$index" @keyup.enter.native="enterFocus('faOneNum',scope.$index)" v-model="ruleForm.productDetail[scope.$index].faOneNum" v-focusAll @change="changeFaOneNum(scope.$index)"></el-input>
                                    </template>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="faOneUnit" label="法定单位" min-width="90">
                                <template slot-scope="scope">
                                    {{scope.row.faOneUnit|filterCjUnit}}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="faTwoNum" label="第二数量" min-width="70">
                                <template slot-scope="scope">
                                    <template v-if="ruleForm.productDetail[scope.$index].faTwoUnit=='035'||ruleForm.productDetail[scope.$index].faTwoUnit==null||ruleForm.productDetail[scope.$index].faTwoUnit==''">
                                        {{ruleForm.productDetail[scope.$index].faTwoNum}}
                                    </template>
                                    <template v-else>
                                        <el-input @input="ruleForm.productDetail[scope.$index].faTwoNum=$util.num(ruleForm.productDetail[scope.$index].faTwoNum)" :id="'faTwoNum'+scope.$index" @keyup.enter.native="enterFocus('faTwoNum',scope.$index)" v-model="ruleForm.productDetail[scope.$index].faTwoNum" v-focusAll @change="changeFaTwoNum(scope.$index)"></el-input>
                                    </template>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" prop="faTwoUnit" label="第二单位" min-width="90">
                                <template slot-scope="scope">
                                    {{scope.row.faTwoUnit|filterCjUnit}}
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </v-block>
                <div class="fix-box" :class="{'cur':fixBox,'isCollapse':isCollapse}">
                    <div>
                        <!--自动计算属性需在html中调用-->
<!--                        <span v-show="false">{{topSumNum}}</span>-->
<!--                        <span v-show="false">{{topSumPrice}}</span>-->
<!--                        <span v-show="false">{{topRoughWeight}}</span>-->
<!--                        <span v-show="false">{{topSuttle}}</span>-->
                        <el-form :model="productItem" :rules="productRules" ref="productItem" label-width="140px" class="demo-ruleForm mt_1 mb_1">
                            <el-row :gutter="10">
                                <el-col :span="8">
                                    <el-form-item label="查品名:" prop="searchName">
                                        <el-input @input="searchProductByNameList" @keyup.enter.native="searchProductByNameList" v-model="productItem.searchName"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="商品名称:" prop="pid">
                                        <el-select filterable default-first-option automatic-dropdown ref="productItemSelect" v-model="productItem.pid" placeholder="请选择">
                                            <el-option
                                                v-for="item in productSearchList"
                                                :key="item.pid"
                                                :label="item.productName+'->'+item.hsCode"
                                                :value="item.pid">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-button type="primary" @click="addProductList('productItem')">确定</el-button>
                                    <div class="fix-box-btn fr" @click="fixBox=!fixBox;isFirstClick++">
                                        <i :class="[fixBox?'el-icon-circle-plus-outline':'el-icon-remove-outline']"></i>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-form>
                        <el-form label-width="140px" class="demo-ruleForm mt_1 mb_1">


                            <el-row class="computed-count mt_1 cl">
                                <el-col :span="5">
                                    <el-form-item label="总件数:">
                                        <el-input v-model="ruleForm.downSumNum" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item label="汇总件数:">
                                        <el-input v-model="ruleForm.downHuiSumNum" :class="{'red-input':ruleForm.downHuiSumNum!=ruleForm.downSumNum}" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item label="件数差:">
                                        <el-input v-model="ruleForm.downDiffer" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item label="总金额:">
                                        <el-input v-model="ruleForm.downSumPrice" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item label="汇总净重:">
                                        <el-input v-model="ruleForm.downSuttle" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row class="computed-count mt_1 cl">
                                <el-col :span="5">
                                    <el-form-item label="总毛重:">
                                        <el-input v-model="ruleForm.downRoughWeight" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item label="汇总毛重:">
                                        <el-input v-model="ruleForm.downHuiRoughWeight" :class="{'red-input':ruleForm.downHuiRoughWeight!=ruleForm.downRoughWeight}" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item label="毛重差:">
                                        <el-input v-model="ruleForm.downRoughWeightDiffer" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="5">
                                    <el-form-item label="汇总金额:">
                                        <el-input v-model="ruleForm.downHuiSumPrice" :class="{'red-input':ruleForm.downHuiSumPrice!=ruleForm.downSumPrice}" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item label="单位净重金额:">
                                        <el-input v-model="ruleForm.downUnitSuttlePrice" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                </div>
                <div style="height: 200px"></div>
            </el-form>
        </div>
        <v-dialog :dialogVisible="dialogVisible" :cancelDialog="cancelDialog" :enterDialog="enterDialog" @dialogStatus="dialogStatus" :title="title">
            <template slot="dialog">
                <el-table :data="boxList" border style="width: 100%">
                    <el-table-column align="center" label="序号" type="index">
                    </el-table-column>
                    <el-table-column align="center" prop="boxName" label="集装箱号">
                    </el-table-column>
                    <el-table-column align="center" prop="cabinetTypeName" label="集装箱规格">
                    </el-table-column>
                    <el-table-column align="center" label="操作">
                        <template slot-scope="scope">
                            <el-button size="mini" type="danger" @click="deleteBoxList(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="box-list">
                    <el-form :model="boxItem" :rules="boxRules" ref="boxItem" label-width="100px" class="demo-ruleForm box-int">
                        <el-row :gutter="10">
                            <el-col :span="12">
                                <el-form-item label="集装箱规格:" prop="cabinetTypeId">
                                    <el-select filterable default-first-option @change="changeCabinetType" v-model="boxItem.cabinetTypeId" placeholder="请选择">
                                        <el-option
                                            v-for="item in cabinetType.options"
                                            :key="item.codeValue"
                                            :label="item.codeName"
                                            :value="item.codeValue">
                                            {{item.label}}
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="集装箱号:" prop="boxName">
                                    <el-input @keyup.enter.native="addBoxList('boxItem')" v-model="boxItem.boxName"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <el-button type="primary" @click="addBoxList('boxItem')">确定</el-button>
                </div>
            </template>
        </v-dialog>
        <v-dialog :dialogVisible="dialogVisible1" :cancelDialog="cancelDialog1" :enterDialog="enterDialog1" @dialogStatus="dialogStatus1" :title="title1">
            <template slot="dialog">
                <el-table :data="productList" border style="width: 100%" ref="multipleTable" @row-click="getTemplateRow">
                    <el-table-column label="" width="60" align="center">
                        <template scope="scope">
                            <el-radio :label="scope.row.pid" v-model="productRadio">&ensp;</el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column prop="hsCode" label="商品编码" width="100"></el-table-column>
                    <el-table-column prop="productName" label="商品名称" width="200"></el-table-column>
                    <el-table-column prop="property" show-overflow-tooltip label="申报要素"></el-table-column>
                </el-table>
            </template>
        </v-dialog>
        <v-dialog :dialogVisible="dialogVisible2" :cancelDialog="cancelDialog2" :enterDialog="enterDialog2" @dialogStatus="dialogStatus2" :title="title2">
            <template slot="dialog">
                <el-form ref="form" :model="form" v-enterToNext="true">
                    <table class="h-table">
                        <tr>
                            <td class="name">商品信息</td>
                            <td>{{product.hsCode}}-{{product.productName}}</td>
                        </tr>
                        <tr>
                            <td colspan="2"><strong>规格型号(根据海关规定,以下要素应全部填报)</strong></td>
                        </tr>
                        <template v-if="property.length>0">
                            <tr v-for="(item,index) in property">
                                <td><i v-if="item.required" class="require">*</i>{{item.propertyName}}</td>
                                <td>
                                    <el-input @keyup.enter.native="index==(property.length-1) && enterDialog2()" ref="propertyInput" v-model="property[index].productValue" v-if="property[index].optionValue=='无'||!property[index].optionValue"></el-input>
                                    <el-select @keyup.enter.native="index==(property.length-1) && enterDialog2()" ref="propertyInput" v-model="property[index].productValue" filterable default-first-option placeholder="请选择" v-else>
                                        <el-option v-for="(item,index) in property[index].optionValue.split(',')" :key="item+index" :label="item" :value="item">
                                        </el-option>
                                    </el-select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">规格型号:{{specification}}</td>
                            </tr>
                        </template>
                        <tr v-else>
                            <td colspan="2">暂无申报要素</td>
                        </tr>
                    </table>
                </el-form>
            </template>
        </v-dialog>
        <v-upload ref="uploadExcel" @uploadExcel="handleUploadExcel" action="/aquaman/trade/excel/readExcel"></v-upload>
    </div>
</template>
<script>
    import vBlock from './../public/Block';
    import vSelect from './../public/SearchSelect';
    import vDialog from './../public/Dialog';
    import vUpload from './../public/Upload';
    // import XLSX from 'xlsx';
    // import {supMode,exemption,modeTran,destinCountry,portDestin,myState,cjType,feeMarkName,unit,insurMarkName,packageType,declareCustoms,deparType,cabinetType,accessory,pinBox,churProAdd,portDeparture,cjUnit} from './../../../util/config';
    let token =  localStorage.getItem('adminToken');
    export default {
        data: function () {
            return {
                ruleForm: {
                    orderId:'',
                    orderType:'1',//贸易方式1一般贸易2市场采购
                    orderPattern:'2',//创建模式1简易模式,2专家模式 ,
                    applyTypeId:1, //申报方式1一体化2非一体化
                    applyTypeName:'一体化',
                    orderNum:'',
                    orderTime:'',
                    myId:'',
                    myName:'',
                    supId:'0110',
                    supMode:'一般贸易',
                    exemptionId:'101',
                    exemption:'一般征税',
                    hyId:'',
                    hyName:'',
                    contractNo:'',
                    excelUrl:'',
                    modeTranId: '',
                    modeTranName: '',
                    churProAddress: '',
                    abId: '',
                    abName: '',
                    abCode: '',
                    abCoTenCode: '',
                    destinCountryId: '',
                    destinCountryName: '',
                    spId: '',
                    spName: '',
                    spCode: '',
                    spCoTenCode: '',
                    portDestinId: '',
                    portDestin: '',
                    overseasCons: 'NO',
                    overseasCode: '',
                    myStateId: '',
                    myStateName: '',
                    cjTypeId: '3',
                    cjTypeName: '3-FOB',
                    freightOne: '',
                    freightTwo: '',
                    freightThree: '',
                    premiumOne: '',
                    premiumTwo: '',
                    premiumThree: '',
                    makeFactory: '',
                    packageTypeId: '22',
                    packageTypeName: '纸制或纤维板制盒/箱',
                    declareCustomsId: '',
                    declareCustomsName: '',
                    deparTypeId: '',
                    deparTypeName: '',
                    topSumNum: '',
                    boatName: '',
                    voyage: '',
                    topRoughWeight: '',
                    billNo: '',
                    closingDate: '',
                    topVolume: '',
                    boxName: '',
                    cabinetTypeId:[],
                    cabinetTypeName:[],
                    topSumPrice: '',
                    pinBoxId: '',
                    pinBoxName: '',
                    portDeparture: '',
                    portDepartureId: '',
                    topSuttle: '',
                    putRecordsNo: '',
                    licenseKey: '',
                    remark: '',
                    otherRemark: '',
                    downSumNum: '',
                    downSumPrice: '',
                    downRoughWeight: '',
                    downHuiSumNum: '',
                    downHuiSumPrice: '',
                    downHuiRoughWeight: '',
                    downDiffer: '',
                    downSuttle: '',
                    downRoughWeightDiffer: '',
                    downUnitSuttlePrice: '',
                    whiteCardNumber: '',
                    passportId: '',
                    passportName: '',
                    licensePlateNumber: '',
                    driverMobile: '',
                    orderStatus: '',
                    productDetail: [
                        {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        },
                        {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        },
                        {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        },
                        {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        },
                        {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        },
                        {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        },
                    ],
                    accessoryList: []
                },
                productDetailFlag:false,   //是否需要删除该列商品
                currentRow:null,   //商品列表选中行
                productList:[],
                product:{},
                productRadio:'',
                productIndex:'',
                productSearchList:[],
                productItem:{
                    searchName: '',
                    pid: '',
                    productName: ''
                },
                productRules:{
                    pid: [
                        { required: true, message: '请选择商品', trigger: 'blur' }
                    ],
                },
                form:{},
                property:[],
                supMode:{
                    options:supMode,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label',
                    }
                },
                exemption:{
                    options:exemption,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label',
                    }
                },
                hy:{
                    options:[],
                    params:{
                        label:'hyName',
                        value:'hyId',
                        key:'hyId',
                        itemName:'hyName'
                    }
                },
                modeTran:{
                    options:modeTran,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                ab:{
                    options:[],
                    params:{
                        label:'abName',
                        value:'abId',
                        key:'abId',
                        itemName:'abName'
                    }
                },
                destinCountry:{
                    options:destinCountry,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                sp:{
                    options:[],
                    params:{
                        label:'spName',
                        value:'spId',
                        key:'spId',
                        itemName:'spName'
                    }
                },
                portDestin:{
                    options:portDestin,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                myState:{
                    options:myState,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                cjType:{
                    options:cjType,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                feeMarkName:feeMarkName,
                unit:{
                    options:unit,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                insurMarkName:insurMarkName,
                packageType:{
                    options:packageType,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                declareCustoms:{
                    options:declareCustoms,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                deparType:{
                    options:deparType,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                cabinetType:{
                    options:cabinetType,
                    params:{
                        label:'codeName',
                        value:'codeValue',
                        key:'codeValue',
                        itemName:'label'
                    }
                },
                pinBox:{
                    options:pinBox,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                portDeparture:{
                    options:portDeparture,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                accessory:{
                    options:accessory,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                churProAdd:{
                    options:churProAdd,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                cjUnit:{
                    options:cjUnit,
                    params:{
                        label:'label',
                        value:'value',
                        key:'value',
                        itemName:'label'
                    }
                },
                block1:true,
                block2:false,
                block3:true,
                dialogVisible:false,
                dialogVisible1:false,
                dialogVisible2:false,
                title:'新增箱号',
                title1:'商品列表',
                title2:'商品规范申报-商品申报要素',
                boxList:[],
                accessoryList:[],
                boxItem:{
                    boxName:'',
                    cabinetTypeId:'',
                    cabinetTypeName:'',
                },
                accessoryItem:{
                    accessoryName: '',
                    accessoryCode: '',
                    accessoryNumber: ''
                },
                loading:'',
                header: {token: token},
                rules: {
                    orderTime: [
                        { required: true, message: '请选择单据日期', trigger: 'change' }
                    ],
                    myName: [
                        { required: true, message: '未获取公司信息,请重新载入页面', trigger: 'blur' }
                    ],
                    supId: [
                        { required: true, message: '请选择监管方式', trigger: 'blur' }
                    ],
                    exemptionId: [
                        { required: true, message: '请选择征免性质', trigger: 'blur' }
                    ],
                    hyId: [
                        { required: true, message: '请选择货运公司', trigger: 'blur' }
                    ],
                    contractNo: [
                        { required: true, message: '请填写合同协议号', trigger: 'blur' }
                    ],
                    modeTranId: [
                        { required: true, message: '请选择运输方式', trigger: 'blur' }
                    ],
                    abId: [
                        { required: true, message: '请选择境内收发货人', trigger: 'blur' }
                    ],
                    abCode: [
                        { required: true, message: '请选择境内收发货人', trigger: 'blur' }
                    ],
                    // destinCountryId: [
                    //     { required: true, message: '请选择运抵国', trigger: 'blur' }
                    // ],
                    spId: [
                        { required: true, message: '请选择生产销售单位', trigger: 'blur' }
                    ],
                    spCode: [
                        { required: true, message: '请选择生产销售单位', trigger: 'blur' }
                    ],
                    // portDestinId: [
                    //     { required: true, message: '请选择指运港', trigger: 'blur' }
                    // ],
                    // myStateId: [
                    //     { required: true, message: '请选择贸易国别', trigger: 'blur' }
                    // ],
                    cjTypeId: [
                        { required: true, message: '请选择成交方式', trigger: 'blur' }
                    ],
                    freightOne: [
                        { required: true, message: '请选择运费种类', trigger: 'change' }
                    ],
                    freightTwo: [
                        { required: true, message: '请输入运费', trigger: 'blur' }
                    ],
                    freightThree: [
                        { required: true, message: '请选择货币单位', trigger: 'change' }
                    ],
                    premiumOne: [
                        { required: true, message: '请选择保险费种类', trigger: 'change' }
                    ],
                    premiumTwo: [
                        { required: true, message: '请输入保险费', trigger: 'blur' }
                    ],
                    premiumThree: [
                        { required: true, message: '请选择货币单位', trigger: 'change' }
                    ],
                    // makeFactory: [
                    //     { required: true, message: '请输入开票工厂', trigger: 'blur' }
                    // ],
                    packageTypeId: [
                        { required: true, message: '请选择包装种类', trigger: 'blur' }
                    ],
                    // declareCustomsId: [
                    //     { required: true, message: '请选择申报海关', trigger: 'blur' }
                    // ],
                    // deparTypeId: [
                    //     { required: true, message: '请选择出境关别', trigger: 'blur' }
                    // ],
                    topSumNum: [
                        { required: true, message: '请输入总件数', trigger: 'blur' },

                        {pattern:/^[+]{0,1}(\d+)$/,message: '请输入正确的件数', trigger: 'blur'}
                    ],
                    topRoughWeight: [
                        { required: true, message: '请输入总毛重', trigger: 'blur' },
                        {pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message: '请输入正确的总毛重', trigger: 'blur'}
                    ],
                    topSuttle: [
                        { required: true, message: '请输入总净重', trigger: 'blur' },
                        {pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message: '请输入正确的总净重', trigger: 'blur'}
                    ],
                    billNo: [
                        { required: true, message: '请输入提单号', trigger: 'blur' }
                    ],
                    topSumPrice: [
                        {pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message: '请输入正确的总金额', trigger: 'blur'}
                    ],
                    portDepartureId: [
                        { required: true, message: '请选择离境口岸', trigger: 'blur' }
                    ],
                    topVolume: [
                        {pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message: '请输入正确的体积', trigger: 'blur'}
                    ],

                    excelUrl: [
                        { required: true, message: '请上传原始货物清单', trigger: 'change' }
                    ]
                },
                boxRules:{
                    boxName: [
                        { required: true, message: '请输入箱号', trigger: 'blur' }
                    ],
                    cabinetTypeId: [
                        { required: true, message: '请选择柜型', trigger: 'blur' }
                    ],
                },
                accessoryRules:{
                    accessoryCode: [
                        { required: true, message: '请选择随附单证代码', trigger: 'blur' }
                    ],
                    accessoryNumber: [
                        { required: true, message: '请输入随附单证编码', trigger: 'blur' }
                    ],
                },


                roughWeight:'',//记录毛重原值
                suttle:'',//记录净重原值
                volumeTrade:'',//记录成交数量原值
                fixBox:true,//查品名窗口缩放
                isFirstClick:0,
                isBottom:false,//是否滑动到最底部
            }
        },
        computed:{
            isCollapse(){
                return this.$store.state.isCollapse;
            },
            specification:function(){
                let property='',len = this.property?this.property.length:0;
                for(let i=0;i< len;i++){
                    if(i==len-1){
                        property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue:'';
                    }else{
                        property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue+'|':'';
                    }
                }
                return property
            },
            /*//计算总件数
            topSumNum:function(){
                let topSumNum='',len = this.ruleForm.productDetail?this.ruleForm.productDetail.length:0;
                for(let i=0;i< len;i++){
                    if(this.ruleForm.productDetail[i].numberPackages!=''&&this.ruleForm.productDetail[i].numberPackages!=null)topSumNum = this.cal.accAdd(topSumNum,this.ruleForm.productDetail[i].numberPackages)
                }
                this.ruleForm.topSumNum = topSumNum;
                this.ruleForm.downSumNum = topSumNum;
                return topSumNum
            },
            //计算总毛重
            topRoughWeight:function(){
                let topRoughWeight='',len = this.ruleForm.productDetail?this.ruleForm.productDetail.length:0;
                for(let i=0;i< len;i++){
                    if(this.ruleForm.productDetail[i].roughWeight!=''&&this.ruleForm.productDetail[i].roughWeight!=null) topRoughWeight=this.cal.accAdd(topRoughWeight,parseFloat(this.ruleForm.productDetail[i].roughWeight));
                }
                this.ruleForm.topRoughWeight = topRoughWeight;
                this.ruleForm.downRoughWeight = topRoughWeight;
                return topRoughWeight
            },
            //计算总金额
            topSumPrice:function(){
                let topSumPrice='',len = this.ruleForm.productDetail?this.ruleForm.productDetail.length:0;
                for(let i=0;i< len;i++){
                    if(this.ruleForm.productDetail[i].totalPrices!=''&&this.ruleForm.productDetail[i].totalPrices!=null) topSumPrice=this.cal.accAdd(topSumPrice,parseFloat(this.ruleForm.productDetail[i].totalPrices));
                }
                this.ruleForm.topSumPrice = topSumPrice;
                this.ruleForm.downSumPrice = topSumPrice;
                return topSumPrice
            },
            //计算总净重
            topSuttle:function(){
                let topSuttle='',len = this.ruleForm.productDetail?this.ruleForm.productDetail.length:0;
                for(let i=0;i< len;i++){
                    if(this.ruleForm.productDetail[i].suttle!=''&&this.ruleForm.productDetail[i].suttle!=null) topSuttle=this.cal.accAdd(topSuttle,parseFloat(this.ruleForm.productDetail[i].suttle));
                }
                this.ruleForm.topSuttle = topSuttle;
                return topSuttle
            },*/
        },
        watch: {
            // 'ruleForm.productDetail': {
            //     handler(newValue, oldValue) {
            //        this.autoCurrencyId();
            //     },
            //     deep: true
            // }
        },
        mounted(){
            this.$store.commit('CollapseSidebar',true);//关闭侧边栏
            let myDate = new Date();
            this.ruleForm.orderTime = myDate.getFullYear()+'-'+(myDate.getMonth()+1<10?('0'+(myDate.getMonth()+1)):(myDate.getMonth()+1))+'-'+(myDate.getDate()<10?('0'+myDate.getDate()):myDate.getDate());
            document.getElementById('content').addEventListener('scroll', this.handleScroll, true);
            let self = this;
            if(this.$route.query.id){
                this.ruleForm.orderId = this.$route.query.id;
                self.getHyList()
                    .then(function () {
                        console.log('获取贸易公司完毕')
                        return self.getAbList();
                    })
                    .then(function () {
                        console.log('获取境内收货人完毕')
                        return self.getSpList();
                    })
                    .then(function () {
                        console.log('获取生产销售单位完毕')
                        return self.getDetail();
                    });
            }else{
                this.getHyList()
                    .then(function () {
                        return self.getAbList();
                    })
                    .then(function () {
                        return self.getSpList();
                    })
                    .then(function () {
                        self.getOrderNum();
                        self.getCompany();
                        self.getDefault();
                    });
            }
        },
        destroyed: function () {
            document.getElementById('content').removeEventListener('scroll', this.handleScroll);   //  离开页面清除（移除）滚轮滚动事件
        },
        methods:{
            handleScroll: function () {
                let clientHeight = document.documentElement.clientHeight || document.body.clientHeight;

                let scrollObj = document.getElementById("content");
                let scrollTop = scrollObj.scrollTop;
                let scrollHeight = scrollObj.scrollHeight;
                //滚动条到底部的条件
                if(scrollTop+clientHeight == scrollHeight+60){
                    // div 到头部的距离 + 屏幕高度 = 可滚动的总高度
                    this.fixBox=false;  //滑动到最底部将品名窗显示出来
                }else{
                    if(this.isFirstClick==0)this.fixBox=true;  //滑动到最底部将品名窗显示出来
                }
            },
            openUploadExcel(){
                this.$refs.uploadExcel.openUpload();
            },
            handleUploadExcel(data){
                this.$set(this.ruleForm,'productDetail',data.productDetail);
            },
            getDetail(){
                let self = this,url = '/trade/comreceiptorder/info/'+self.ruleForm.orderId;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        self.ruleForm = self.$util.extend(res.comReceiptOrder);
                        let boxName=self.ruleForm.boxName?self.ruleForm.boxName.split(','):[];
                        let cabinetTypeId=self.ruleForm.cabinetTypeId?self.ruleForm.cabinetTypeId.split(','):[];
                        self.ruleForm.cabinetTypeId = self.ruleForm.cabinetTypeId?self.ruleForm.cabinetTypeId.split(','):[];
                        // self.ruleForm.cabinetTypeId = self.ruleForm.cabinetTypeId.length>1?self.ruleForm.cabinetTypeId:self.ruleForm.cabinetTypeId[0];
                        let cabinetTypeName=self.ruleForm.cabinetTypeName?self.ruleForm.cabinetTypeName.split(','):[];
                        self.boxList = boxName.map((item,index)=>{
                            console.log(index)
                            return {boxName:boxName[index],cabinetTypeId:cabinetTypeId[index],cabinetTypeName:cabinetTypeName[index]}
                        })
                        self.accessoryList =self.ruleForm.accessoryList;
                        self.autoProdetail();
                    }
                });
            },
            getCompany(){
                let self = this;
                self.$axios.get('/sys/user/info',{headers: {'closeLoading': true}}).then((res) => {
                    if(res.code==0){
                        self.ruleForm.myId = res.user.companyId
                        self.ruleForm.myName = res.user.companyName
                    }else{
                        self.$message({
                            message: "公司信息获取失败,请重新打开页面",
                            type: 'error'
                        })
                    }
                });
            },
            getOrderNum(){
                return
              let self = this;
                self.$axios.get('/trade/comreceiptorder/getOrderNum',{
                    headers: {'closeLoading': true},
                    params:{
                        orderType:1,  //1一般贸易，2市场采购
                        applyTypeId:1 //申报方式1一体化2非一体化
                    }
                }).then((res) => {
                    if(res.code==0){
                        self.ruleForm.orderNum = res.orderNumber;
                    }else{
                        self.$message({
                            message: "单据编号获取失败,请重新打开页面",
                            type: 'error'
                        });
                    }
                });
            },
            getDefault(){
                let self = this;
                self.$axios.get('/trade/abroadconinfo/getDefaultInfo').then((res) => {
                    if(res.code==0){
                        this.ruleForm.abId = res.defaultInfo?res.defaultInfo.abId:'';
                        this.ruleForm.abName = res.defaultInfo?res.defaultInfo.abName:'';
                        this.ruleForm.abCode = res.defaultInfo?res.defaultInfo.customsCode:'';
                        this.ruleForm.abCoTenCode = res.defaultInfo&&res.defaultInfo.coTenCode?res.defaultInfo.coTenCode:'';
                    }
                });
                self.$axios.get('/trade/productsellunits/getDefaultInfo').then((res) => {
                    if(res.code==0){
                        this.ruleForm.spId = res.defaultInfo?res.defaultInfo.spId:'';
                        this.ruleForm.spName = res.defaultInfo?res.defaultInfo.spName:'';
                        this.ruleForm.spCode = res.defaultInfo?res.defaultInfo.customsCode:'';
                        this.ruleForm.spCoTenCode = res.defaultInfo&&res.defaultInfo.coTenCode?res.defaultInfo.coTenCode:'';
                    }
                });

            },
            //搜索匹配各字段方法
            searchSupMode(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeSupMode(vId){
                let obj = {};
                obj = this.supMode.options.find((item)=>{
                    return item.codeValue === vId;
                });
                this.ruleForm.supMode = obj.codeName;
            },

            searchExemption(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeExemption(vId){
                let obj = {};
                obj = this.exemption.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.exemption = obj.label;
            },

            getHyList(search){
                let self = this;
                return new Promise(function(resolve, reject){
                    self.$axios.get('/trade/comreceiptorder/hyList',{headers: {'closeLoading': true},params:{hyName:search}}).then((res) => {
                        if(res.code==0){
                            self.hy.options = res.freight;
                            resolve();
                        }

                    });
                });

            },
            searchHy(){
                return true;
            },
            changeHy(vId){
                let obj = {};
                obj = this.hy.options.find((item)=>{
                    return item.hyId === vId;
                });
                this.ruleForm.hyName = obj.hyName;
            },

            searchModeTran(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeModeTran(vId){
                let obj = {};
                obj = this.modeTran.options.find((item)=>{
                    return item.codeValue === vId;
                });
                this.ruleForm.modeTranName = obj.codeName;
            },

            getAbList(search){
                let self = this;
                return new Promise(function(resolve, reject){
                    self.$axios.get('/trade/abroadconinfo/list',{headers: {'closeLoading': true},params:{abName:search,page:1,limit:50}}).then((res) => {
                        if(res.code==0){
                            self.ab.options = res.page.list;
                            resolve();
                        }
                    });
                });
            },
            searchAb(){
                return true;
            },
            changeAb(vId){
                let obj = {};
                obj = this.ab.options.find((item)=>{
                    return item.abId === vId;
                });
                this.ruleForm.abName = obj.abName;
                this.ruleForm.abCode = obj.customsCode;
                this.ruleForm.abCoTenCode = obj.coTenCode?obj.coTenCode:'';
            },

            searchDestinCountry(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.cusCode.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeDestinCountry(vId){
                let obj = {};
                obj = this.destinCountry.options.find((item)=>{
                    return item.codeValue === vId;
                });
                this.ruleForm.destinCountryName = obj.codeName;
                // this.ruleForm.myStateId = vId;
                // this.ruleForm.myStateName = obj.codeName;
            },

            getSpList(search){
                let self = this;
                return new Promise(function(resolve, reject){
                    self.$axios.get('/trade/productsellunits/list',{headers: {'closeLoading': true},params:{spName:search,page:1,limit:50}}).then((res) => {
                        if(res.code==0){
                            self.sp.options = res.page.list;
                            resolve();
                        }
                    });
                });
            },
            searchSp(){
                return true;
            },
            changeSp(vId){
                let obj = {};
                obj = this.sp.options.find((item)=>{
                    return item.spId === vId;
                });
                this.ruleForm.spName = obj.spName;
                this.ruleForm.spCode = obj.customsCode;
                debugger
                this.ruleForm.spCoTenCode = obj.coTenCode?obj.coTenCode:'';
            },

            searchPortDestin(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.cusCode.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changePortDestin(vId){
                let obj = {},parentObj={};
                obj = this.portDestin.options.find((item)=>{
                    return item.codeValue === vId;
                });
                let cusCode = obj.cusCode;
                parentObj = this.destinCountry.options.find((item)=>{
                    return item.cusCode === cusCode;
                });
                this.ruleForm.portDestin = obj.codeName;
                this.ruleForm.destinCountryId = parentObj?parentObj.codeValue:'';
                this.ruleForm.destinCountryName = parentObj?parentObj.codeName:'';
                // this.ruleForm.myStateId = parentObj?parentObj.codeValue:'';
                // this.ruleForm.myStateName = parentObj?parentObj.codeName:'';
            },

            searchMyState(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.cusCode.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeMyState(vId){
                let obj = {};
                obj = this.myState.options.find((item)=>{
                    return item.codeValue === vId;
                });
                this.ruleForm.myStateName = obj.codeName;
            },

            searchCjType(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeCjType(vId){
                if(vId==2){
                    this.ruleForm.premiumOne='';
                    this.ruleForm.premiumTwo='';
                    this.ruleForm.premiumThree='';
                }else if(vId==3){
                    this.ruleForm.freightOne='';
                    this.ruleForm.freightTwo='';
                    this.ruleForm.freightThree='';
                    this.ruleForm.premiumOne='';
                    this.ruleForm.premiumTwo='';
                    this.ruleForm.premiumThree='';
                }else if(vId==4){
                    this.ruleForm.freightOne='';
                    this.ruleForm.freightTwo='';
                    this.ruleForm.freightThree='';
                }
                let obj = {};
                obj = this.cjType.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.cjTypeName = obj.label;
            },

            searchFreightThree(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.cusCode.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },

            searchPackageType(obj,search){
                return obj.codeValue.toLowerCase().indexOf(search.toLowerCase())>=0||obj.codeName.toLowerCase().indexOf(search.toLowerCase())>=0||obj.cusCode.toLowerCase().indexOf(search.toLowerCase())>=0||obj.ciqCode.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changePackageType(vId){
                let obj = {};
                obj = this.packageType.options.find((item)=>{
                    return item.codeValue === vId;
                });
                this.ruleForm.packageTypeName = obj.codeName;
            },

            searchDeclareCustoms(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeDeclareCustoms(vId){
                let obj = {};
                obj = this.declareCustoms.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.declareCustomsName = obj.label;
            },

            searchDeparType(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeDeparType(vId){
                let obj = {};
                obj = this.deparType.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.deparTypeName = obj.label;
            },

            searchPinBox(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changePinBox(vId){
                let obj = {};
                obj = this.pinBox.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.pinBoxName = obj.label;
            },

            searchPortDeparture(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changePortDeparture(vId){
                let obj = {};
                obj = this.portDeparture.options.find((item)=>{
                    return item.value === vId;
                });
                this.ruleForm.portDeparture = obj.label;
            },

            searchAccessory(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeAccessory(vId){
                let obj = {};
                obj = this.accessory.options.find((item)=>{
                    return item.value === vId;
                });
                this.accessoryItem.accessoryName = obj.label;
            },

            searchChurProAdd(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },
            changeChurProAdd(vId){
                let obj = {};
                obj = this.churProAdd.options.find((item)=>{
                    return item.value === vId;
                });
                // this.accessoryItem.accessoryName = obj.label;
            },


            searchCjUnit(obj,search){
                return obj.value.toLowerCase().indexOf(search.toLowerCase())>=0||obj.label.toLowerCase().indexOf(search.toLowerCase())>=0;
            },

            changeCabinetType(){
                let obj = {};
                obj = this.cabinetType.options.find((item)=>{
                    return item.codeValue === this.boxItem.cabinetTypeId;
                });
                this.boxItem.cabinetTypeName = obj.codeName;
            },
            changeRuleFormCabinetType(){

                let obj = {};
                obj = this.cabinetType.options.find((item)=>{
                    return item.codeValue === this.ruleForm.cabinetTypeId[0];
                });
                this.ruleForm.cabinetTypeName[0] = obj?obj.codeName:'';
            },

            //箱号处理
            dialogStatus(val){
                this.dialogVisible = val;
            },
            cancelDialog(){
                this.dialogVisible = false;
            },
            enterDialog(){
                this.dialogVisible = false;
                let boxName=[],cabinetTypeId=[],cabinetTypeName=[];

                    this.boxList.map((item)=>{
                        boxName.push(item.boxName)
                        cabinetTypeId.push(item.cabinetTypeId)
                        cabinetTypeName.push(item.cabinetTypeName)
                    });
                    this.ruleForm.boxName = boxName.join(',');
                    this.ruleForm.cabinetTypeId = cabinetTypeId;
                    this.ruleForm.cabinetTypeName = cabinetTypeName;

            },

            addAccessoryList(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let accessoryCode = self.accessoryList.find((item)=>{
                            return item.accessoryCode ===self.accessoryItem.accessoryCode
                        });
                        if(accessoryCode){
                            self.$message({
                                message: '单证编号不可重复,请重新输入!',
                                type: 'error'
                            });
                            self.accessoryItem.accessoryCode='';
                            self.accessoryItem.accessoryName='';
                            return
                        }
                        let accessoryItem = self.$util.extend(self.accessoryItem)
                        self.accessoryList.push(accessoryItem);
                        self.accessoryItem.accessoryNumber='';
                        self.accessoryItem.accessoryCode='';
                        self.accessoryItem.accessoryName='';
                        self.ruleForm.accessoryList=self.accessoryList.map((item)=>{
                            return {accessoryName:item.accessoryName,accessoryCode:item.accessoryCode,accessoryNumber:item.accessoryNumber}
                        });
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            deleteAccessoryList(index){
                this.accessoryList.splice(index,1);
                this.ruleForm.accessoryList=this.accessoryList.map((item)=>{
                    return {accessoryCode:item.accessoryCode,accessoryNumber:item.accessoryNumber}
                });
            },

            showBoxList(){
                if(this.ruleForm.boxName!=''&&this.ruleForm.cabinetTypeId.length==1&&this.boxList.length==0){
                    this.boxList.push({
                        boxName:this.ruleForm.boxName,
                        cabinetTypeId:this.ruleForm.cabinetTypeId[0],
                        cabinetTypeName:this.ruleForm.cabinetTypeName[0],
                    })
                }
                this.dialogVisible = true;
            },
            addBoxList(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let boxName = self.boxList.find((item)=>{
                            return item.boxName ===self.boxItem.boxName
                        });
                        if(boxName){
                            self.$message({
                                message: '箱号已存在,请重新输入',
                                type: 'error'
                            });
                            self.boxItem.boxName = '';
                            return
                        }
                        let boxItem = self.$util.extend(self.boxItem)
                        self.boxList.push(boxItem);
                        self.boxItem.boxName='';
                        self.boxItem.cabinetTypeId='';
                        self.boxItem.cabinetTypeName='';
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            deleteBoxList(index){
                this.boxList.splice(index,1);
            },



            // 同属性下一行获得焦点
            enterFocus(name,index){
                index +=1;
                let element = document.getElementById(name+index);
                if(element)element.focus();
            },

            //搜索商品
            searchProduct(index){
                let self = this;let params={};
                self.productIndex = index;
                params.page=1;
                params.limit=10;
                params.orderType=1;//orderType   1一般贸易 市场采购
                params.keyWord=self.ruleForm.productDetail[index].hsCode;
                self.$set(self.ruleForm.productDetail[self.productIndex],'pid','');
                if(params.keyWord==''){
                    self.$message({
                        type: 'error',
                        message: '请输入商品编码!'
                    });
                    return
                }
                if(params.keyWord.length<4){
                    self.$message({
                        type: 'error',
                        message: '请至少输入四位商品编码!'
                    });
                    return
                }
                params.keyType = 2;
                self.$axios.get('/trade/tradeproductinfo/searchHsCode', {params:params}).then((res) => {
                    if(res.page.list.length==0){
                        self.$message({
                            type: 'error',
                            message: '不存在该编码!'
                        });
                        self.ruleForm.productDetail[index].hsCode=''
                        return
                    }
                    if(res.page.list.length==1){
                        self.getTemplateRow(res.page.list[0]);
                        if(!res.page.list[0].property){
                            self.enterDialog1();
                        }else{
                            self.setProduct(self.ruleForm.productDetail[self.productIndex],res.page.list[0]);
                        }
                    }else{
                        self.productList = res.page.list;
                        self.dialogVisible1 = true;
                    }

                });
            },

            //商品列表选中行
            // handleCurrentChange(row){
            //     this.currentRow = row;
            //     if(this.currentRow.productName!=''&&this.currentRow.productName!=null){
            //         this.productItem.searchName = this.currentRow.productName;
            //         this.searchProductByNameList(this.currentRow.productName);
            //     }
            //
            // },
            //表格中enter品名
            enterProductName(row,index){
                let self = this;
                self.currentRow = row;
                if(self.currentRow.productName!=''&&self.currentRow.productName!=null){
                    let params={};
                    params.page=1;
                    params.limit=10;
                    params.orderType=1;//orderType   1一般贸易 市场采购
                    params.keyWord = self.currentRow.productName;
                    params.keyType = 3;
                    self.$axios.get('/trade/tradeproductinfo/searchHsCode', {params:params,headers: {'closeLoading': true}}).then((res) => {
                        debugger
                        if (res.page.list.length ==1&&res.page.list[0].productName==params.keyWord) {
                            self.setProduct(self.currentRow,res.page.list[0])
                        }
                        document.getElementById('productName'+(index+1)).focus();
                    })
                }
            },

            searchProductName(row){
                this.currentRow = row;
                if(this.currentRow.productName!=''&&this.currentRow.productName!=null){
                    this.productItem.searchName = this.currentRow.productName;
                    this.searchProductByNameList(this.currentRow.productName);
                }
            },


            searchProductByNameList(name){
                let self = this;let params={};
                params.page=1;
                params.limit=10;
                params.orderType=1;//orderType   1一般贸易 市场采购
                params.keyWord = self.productItem.searchName;
                params.keyType = 1;
                self.$axios.get('/trade/tradeproductinfo/searchHsCode', {params:params,headers: {'closeLoading': true}}).then((res) => {
                    if(res.page.list.length==0) {
                        self.productItem.pid = '';
                        self.productSearchList = [];
                        return
                    }
                    self.productItem.pid = '';
                    self.productSearchList = res.page.list;
                    // self.$refs.productItemSelect.focus();
                });
            },

            deleteProduct(e,index){
                window.event?window.event.cancelBubble=true:e.stopPropagation();
                this.$confirm('是否删除该商品,?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error'
                }).then(() => {
                    this.ruleForm.productDetail.splice(index,1);
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                });
            },
            addProduct(e){
                window.event?window.event.cancelBubble=true:e.stopPropagation();
                let obj = {
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        };
                this.ruleForm.productDetail.push(obj);
            },

            addProductList(formName){
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        self.product = self.productSearchList.filter(item => item.pid ==self.productItem.pid)[0];
                        if(self.currentRow==null){
                            self.$message({
                                message: '请选择需要操作的列表行',
                                type: 'error'
                            });
                        }
                        self.setProduct(self.currentRow,self.product)
                        self.$refs['productItem'].resetFields();
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },

            changeProperty(e,product,index){
                window.event?window.event.cancelBubble=true:e.stopPropagation();
                let self = this,_property;
                if(!product.hsCode||product.hsCode.length!=10){
                    return
                }
                self.product=product;
                self.productIndex = index;
                if(product.property!=''&&product.property!=null){
                    _property = product.property.split('|').map((item)=>{
                        return item.split('=');
                    });
                }else{
                    _property=[]
                }
                self.getProperByCode(_property);
            },

            dialogStatus1(val){
                this.dialogVisible1 = val;
            },
            cancelDialog1(){
                this.dialogVisible1 = false;
            },
            enterDialog1(){
                let self = this,_property;
                self.dialogVisible1 = false;
                if(self.product.property!=''&&self.product.property!=null){
                    // self.setProduct(self.ruleForm.productDetail[self.productIndex],self.product);
                    // return
                    _property = self.product.property.split('|').map((item)=>{
                        return item.split('=');
                    });
                }else{
                    _property=[]
                }
                self.getProperByCode(_property);
            },
            getProperByCode(_property){
                let self = this,url = '/trade/tradeproductinfo/getProperByCode/' + self.product.hsCode;
                self.$axios.get(url).then((res) => {
                    self.property = res.proper;
                    let len = self.property.length,_len = _property.length;
                    for(let i=0;i<len;i++){
                        for(let j=0;j<_len;j++){
                            if(self.property[i].propertyNo==parseInt(_property[j][0])){
                                self.$set(self.property[i],'productValue',_property[j][1]);
                                break
                            }else{

                                self.$set(self.property[i],'productValue','');
                            }
                        }
                    }
                    self.dialogVisible2 = true;
                    this.$nextTick(function () {
                        this.$refs.propertyInput[0].focus();
                    });
                });
            },
            dialogStatus2(val){
                this.dialogVisible2 = val;
            },
            cancelDialog2(){
                this.dialogVisible2 = false;
            },
            enterDialog2(){
                let self = this,len = self.property.length,property='';
                if(len!=0){
                    for(let i=0;i< len;i++){
                        if(!self.property[i].productValue&&self.property[i].required==1){
                            let msg = '请填写'+self.property[i].propertyName;
                            self.$message({
                                type: 'error',
                                message: msg
                            });
                            return false
                        }else{
                            if(i==len-1){
                                property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue:'';
                            }else{
                                property += this.property[i].productValue?this.property[i].propertyNo+'='+this.property[i].productValue+'|':'';
                            }
                        }
                    }
                }

                let params = {
                    "pid": self.product.pid,
                    "property": property
                }
                self.product.property = property;
                self.setProduct(self.ruleForm.productDetail[self.productIndex],self.product);
                self.dialogVisible2 = false;
                document.getElementById('numberPackages'+self.productIndex).focus();
                // self.$axios.post('/trade/tradeproductinfo/updateProperty',params).then((res)=>{});
            },


            //商品单选
            getTemplateRow(row, event, column){                                 //获取选中数据
                this.product=row;
                this.productRadio = row.pid;
            },

//箱号处理


            //给商品赋值
            setProduct(ruleFormProduct,product,ignoreName=true){
                for(let name in ruleFormProduct){
                    if(ignoreName&&name=='productName'&&ruleFormProduct['productName']!='')continue
                    let val
                    if(name=='cjUnitId'){
                        val = (product['cjUnit']!==''||product['cjUnit']!=null||product['cjUnit']!=undefined)?product['cjUnit']:'';
                    }else{
                        val = (product[name]!==''||product[name]!=null||product[name]!=undefined)?product[name]:'';
                    }
                    if(val===''||val==null)continue
                    this.$set(ruleFormProduct,name,val);
                }
                // let _attr=attr?attr:['pid','hsCode','productName','property','cjUnit','faOneUnit','faTwoUnit'];
                // for(let i=0;i<_attr.length;i++){
                //     let val = product[_attr[i]]?product[_attr[i]]:'';
                //     if(!val)continue
                //     if(_attr[i]=='cjUnit'){
                //         this.$set(ruleFormProduct,'cjUnitId',val);
                //     }else{
                //         this.$set(ruleFormProduct,_attr[i],val);
                //     }
                //
                // }
            },


            //货物清单联动方法集合
            isRealNum(val){
                // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除
                if(val === "" || val ==null){
                    return false;
                }
                if(!isNaN(val)){
                    return true;
                }else{
                    return false;
                }
            },


            changeRoughWeight(index) {
                if (parseFloat(this.ruleForm.productDetail[index].suttle) > parseFloat(this.ruleForm.productDetail[index].roughWeight)) {
                    this.$message({message: '净重大于毛重', type: 'error'});
                    this.ruleForm.productDetail[index].roughWeight = this.roughWeight
                }
            },
            changeSuttle(index) {
                if (parseFloat(this.ruleForm.productDetail[index].suttle) > parseFloat(this.ruleForm.productDetail[index].roughWeight)) {

                    this.$message({message: '净重大于毛重', type: 'error'});
                    this.ruleForm.productDetail[index].suttle = this.suttle
                }else{
                    this.calVolumeTrade(index)
                    this.calFaOneNum(index)
                    this.calFaTwoNum(index)
                }
            },

            changeVolumeTrade(index){
                // 成交单位为千克时
                if(this.ruleForm.productDetail[index].cjUnitId=='035'){
                    if(parseFloat(this.ruleForm.productDetail[index].volumeTrade)>parseFloat(this.ruleForm.productDetail[index].roughWeight)){
                        this.$message({message:'成交单位为千克,净重大于毛重',type:'error'});
                        this.ruleForm.productDetail[index].volumeTrade = this.volumeTrade
                    }else{
                        //净重
                        this.ruleForm.productDetail[index].suttle =this.ruleForm.productDetail[index].volumeTrade;
                        this.calFaOneNum(index)
                        this.calFaTwoNum(index)
                    }
                }else{
                    this.ruleForm.productDetail[index].volumeTrade = Math.round(this.ruleForm.productDetail[index].volumeTrade);
                    this.calIsEqFaOneNum(index);
                    this.calIsEqFaTwoNum(index);
                }
                //单价
                this.calUnitPrice(index)

            },

            changeCjUnitId(index){
                if(this.ruleForm.productDetail[index].cjUnitId=='035'){
                    //成交数量=净重
                    this.ruleForm.productDetail[index].volumeTrade = this.ruleForm.productDetail[index].suttle;
                    this.calUnitPrice(index)
                }else{
                    this.ruleForm.productDetail[index].volumeTrade = Math.round(this.ruleForm.productDetail[index].volumeTrade);
                    this.calIsEqFaOneNum(index);
                    this.calIsEqFaTwoNum(index);
                }
            },

            //修改单价
            changeUnitPrice(index){
                if(this.isRealNum(this.ruleForm.productDetail[index].volumeTrade)&&this.isRealNum(this.ruleForm.productDetail[index].unitPrice))
                    this.ruleForm.productDetail[index].totalPrices=this.cal.accMul(this.ruleForm.productDetail[index].volumeTrade,this.ruleForm.productDetail[index].unitPrice).toFixed(2);
            },
            //修改总价
            changeTotalPrices(index){
                if(this.isRealNum(this.ruleForm.productDetail[index].totalPrices)&&this.isRealNum(this.ruleForm.productDetail[index].volumeTrade))
                    this.ruleForm.productDetail[index].unitPrice=this.cal.accDiv(this.ruleForm.productDetail[index].totalPrices,this.ruleForm.productDetail[index].volumeTrade).toFixed(4);
            },
            //修改法定数量
            changeFaOneNum(index){
                if(this.ruleForm.productDetail[index].cjUnitId==this.ruleForm.productDetail[index].faOneUnit){
                    if(this.isRealNum(this.ruleForm.productDetail[index].faOneNum)){
                        //成交数量=法定数量
                        this.ruleForm.productDetail[index].volumeTrade = Math.round(this.ruleForm.productDetail[index].faOneNum);
                        //单价=金额/成交数量
                        this.calUnitPrice(index);
                    }
                }
            },
            //修改法定第二数量
            changeFaTwoNum(index){
                if(this.ruleForm.productDetail[index].cjUnitId==this.ruleForm.productDetail[index].faTwoUnit){
                    if(this.isRealNum(this.ruleForm.productDetail[index].faTwoNum)){
                        //成交数量=法定数量
                        this.ruleForm.productDetail[index].volumeTrade = Math.round(this.ruleForm.productDetail[index].faTwoNum);
                        //单价=金额/成交数量
                        this.calUnitPrice(index);
                    }
                }
            },




            //计算成交数量
            calVolumeTrade(index){
                if(this.ruleForm.productDetail[index].cjUnitId=='035'){
                    //成交数量=净重
                    this.ruleForm.productDetail[index].volumeTrade = this.ruleForm.productDetail[index].suttle;
                    //单价=总价/成交数量
                    this.calUnitPrice(index)
                }
            },
            //计算法定数量
            calFaOneNum(index){
                if(this.ruleForm.productDetail[index].faOneUnit=='035'){
                    //法定数量=净重
                    this.ruleForm.productDetail[index].faOneNum = this.ruleForm.productDetail[index].suttle;
                }
            },
            //计算法定第二数量
            calFaTwoNum(index){
                if(this.ruleForm.productDetail[index].faTwoUnit=='035'){
                    //法定数量=净重
                    this.ruleForm.productDetail[index].faTwoNum = this.ruleForm.productDetail[index].suttle;
                }
            },


            //计算单价
            calUnitPrice(index){
                if(this.isRealNum(this.ruleForm.productDetail[index].totalPrices)&&this.isRealNum(this.ruleForm.productDetail[index].volumeTrade))
                    this.ruleForm.productDetail[index].unitPrice=this.cal.accDiv(this.ruleForm.productDetail[index].totalPrices,this.ruleForm.productDetail[index].volumeTrade).toFixed(4);
            },

            //是否成交单位相同的法定数量计算方法
            calIsEqFaOneNum(index){
                if(this.ruleForm.productDetail[index].faOneUnit==this.ruleForm.productDetail[index].cjUnitId){
                    //法定数量=成交数量
                    this.ruleForm.productDetail[index].faOneNum = this.ruleForm.productDetail[index].volumeTrade;
                }
            },
            //是否成交单位相同的法定第二数量计算方法
            calIsEqFaTwoNum(index){
                if(this.ruleForm.productDetail[index].faTwoUnit==this.ruleForm.productDetail[index].cjUnitId){
                    //法定数量=成交数量
                    this.ruleForm.productDetail[index].faTwoNum = this.ruleForm.productDetail[index].volumeTrade;
                }
            },


            //记录毛重重原值
            recordRoughWeight(index){
                this.roughWeight = this.ruleForm.productDetail[index].roughWeight;
            },
            //记录净重重原值
            recordSuttle(index){
                this.suttle = this.ruleForm.productDetail[index].suttle;
            },
            //记录成交数量原值
            recordVolumeTrade(index){
                this.volumeTrade = this.ruleForm.productDetail[index].volumeTrade;
            },






            //上传处理
            handleProgress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '正在上传...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            handleError(){
                this.loading.close();
                this.$message({
                    message: "上传失败,请重试",
                    type: 'error'
                })
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handleSuccess(res, file,fileList) {
                this.loading.close();
                fileList[fileList.length-1].name= res.result.substring(res.result.lastIndexOf('/')+1);
                fileList[fileList.length-1].url= res.result;
                this.ruleForm.excelUrl=fileList;
            },
            beforeRemove(file, fileList) {
                return this.$confirm(`确定移除 ${ file.name }？`);
            },

            //率格式控制
            //率,用户录入范围是0.0001-99，代表费率是0.0001%-99%
            //单价, 整数最多录入10位，小数点后面最多录入4位。
            //总价,整数最多录入12位，小数点后面最多录入4位。
            rateStyle(mark,rate){
                let markVal = this.ruleForm[mark],rateVal = this.ruleForm[rate];
                let reg1 = /^\d{0,10}(\.\d{0,4})?$/;
                let reg2 = /^\d{0,12}(\.\d{0,4})?$/;
                let reg4 = /^(\-?)\d{0,2}(\.\d{0,4})?$/;//费率长度限制， 整数最多录入2位，小数点后面最多录入4位。
                if (markVal == 1) {
                    if(rateVal){
                        if (rateVal < 0.0001 || rateVal > 99 || !reg4.test(rateVal)) {
                            this.$message({
                                message: '录入值范围为0.0001-99',
                                type: 'error'
                            });
                            this.ruleForm[rate]='';
                        }
                    }
                } else if (markVal == 2) {
                    if (!reg1.test(rateVal)) {
                        this.$message({
                            message: '整数最多录入10位，小数点后面最多录入4位',
                            type: 'error'
                        });
                        this.ruleForm[rate]='';
                    }
                } else if (markVal == 3) {
                    if (!reg2.test(rateVal)) {
                        this.$message({
                            message: '整数最多录入12位，小数点后面最多录入4位',
                            type: 'error'
                        });
                        this.ruleForm[rate]='';
                    }
                }
            },
            //导入

            introduction(event){

                if(!event.currentTarget.files.length) {
                    return;
                }
                let self = this;
                // 拿取文件对象
                let f = event.currentTarget.files[0];
                // 用FileReader来读取
                let reader = new FileReader();
                // 重写FileReader上的readAsBinaryString方法
                FileReader.prototype.readAsBinaryString = function(f) {
                    let binary = "";
                    let wb; // 读取完成的数据
                    let outdata; // 你需要的数据
                    let reader = new FileReader();
                    reader.onload = function(e) {
                        // 读取成Uint8Array，再转换为Unicode编码（Unicode占两个字节）
                        let bytes = new Uint8Array(reader.result);
                        let length = bytes.byteLength;
                        for (let i = 0; i < length; i++) {
                            binary += String.fromCharCode(bytes[i]);
                        }
                        // 接下来就是xlsx了，具体可看api
                        wb = XLSX.read(binary, {
                            type: "binary"
                        });
                        outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
                        outdata.map((v,index) => {
                            if(index<2)return
                            let obj = {}
                            obj.productName = v['__EMPTY_4']?v['__EMPTY_4']:'';
                            obj.hsCode = v['__EMPTY_2']?v['__EMPTY_2']:'';
                            obj.property = v['__EMPTY_5']?v['__EMPTY_5']:'';
                            // obj.numberPackages = v.件数;
                            // obj.roughWeight = v.毛重;
                            // obj.suttle = v.净重;
                            obj.volumeTrade = v['__EMPTY_6']?v['__EMPTY_6']:'';
                            obj.cjUnitId = v['__EMPTY_7']?v['__EMPTY_7']:'';
                            obj.unitPrice = v['__EMPTY_8']?v['__EMPTY_8']:'';
                            obj.currencyId = v['__EMPTY_10']?v['__EMPTY_10']:'';
                            obj.totalPrices = v['__EMPTY_9']?v['__EMPTY_9']:'';
                            obj.churProAddId = v['__EMPTY_20']?v['__EMPTY_20']:'';
                            obj.faOneUnit = v['__EMPTY_12']?v['__EMPTY_12']:'';
                            obj.faTwoUnit = v['__EMPTY_6']?v['__EMPTY_6']:'';
                            obj.faOneNum = v['__EMPTY_17']?v['__EMPTY_17']:'';
                            obj.faTwoNum = v['__EMPTY_16']?v['__EMPTY_16']:'';
                            self.ruleForm.productDetail.push(obj);
                            self.autoProdetail();
                        });

                    };
                    reader.readAsArrayBuffer(f);
                };
                reader.readAsBinaryString(f);
            },

            //检测商品长度自动补齐
            autoProdetail(){
                let self =this;
                if(self.ruleForm.productDetail==null){
                    self.ruleForm.productDetail=[];
                    for(let i=0;i<6;i++){
                        self.ruleForm.productDetail.push({
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        });
                    }
                }else if(self.ruleForm.productDetail.length<6){
                    for(let i=0;i<6-self.ruleForm.productDetail.length;i++){
                        self.ruleForm.productDetail.push({
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        });
                    }
                }
                if(self.ruleForm.productDetail.length==6)return;
                let len = self.ruleForm.productDetail.length-1,productDetail = self.ruleForm.productDetail;
                for(let i=len;i>=0;i--){
                    self.productDetailFlag = true;
                    for(let key in productDetail[i]){
                        if(productDetail[i][key]!=''&&productDetail[i][key]!=null)
                            self.productDetailFlag = false;
                    }
                    if(self.productDetailFlag) productDetail.splice(i,1);
                }
                let _len = 6-productDetail.length;
                if(_len>0){
                    for(let x=0;x<_len;x++){
                        productDetail.push({
                            pid: '',
                            productName: '',
                            hsCode: '',
                            property: '',
                            numberPackages: '',
                            roughWeight: '',
                            suttle: '',
                            volumeTrade: '',
                            cjUnitId: '',
                            unitPrice: '',
                            currencyId: 'USD',
                            totalPrices: '',
                            churProAddId: '',
                            faOneUnit: '',
                            faTwoUnit: '',
                            faOneNum: '',
                            faTwoNum: ''
                        });
                    }
                }
                // self.ruleForm.productDetail = Object.assign({},self.ruleForm.productDetail,productDetail)
                self.ruleForm.productDetail = productDetail;
            },






            //检测商品列表币种
            autoCurrencyId() {
                let len = this.ruleForm.productDetail.length, ids = [];
                for (let i = 0; i < len; i++) {
                    if (this.ruleForm.productDetail[i].currencyId != '' && this.ruleForm.productDetail[i].currencyId != null) ids.push(this.ruleForm.productDetail[i].currencyId);
                }
                let obj = {};
                for(let j=0;j<ids.length;j++){
                    let item = ids[j];
                    obj[item] = (obj[item] +1 ) || 1;
                }
                if(Object.getOwnPropertyNames(obj).length>1){
                    this.$message({
                        message: '存在多币种',
                        type: 'error'
                    });
                }
            },
            checkProduct(product){
                return new Promise((resolve, reject) => {
                    let obj = product,len = obj.length;
                    if(len==0){
                        this.$message({
                            message: '请填写商品',
                            type: 'error'
                        });
                        resolve(false)
                    }
                    //必填字段数组         件数               毛重         净重     成交数量        单价          总价         法定第一数量
                    let requireArr = ['numberPackages','roughWeight','suttle','volumeTrade','unitPrice','totalPrices','faOneNum']
                    for(let i=0;i<len;i++){
                        for(let attr in obj[i]){
                            if(requireArr.indexOf(attr)<0)continue
                            //必填且为空
                            if(requireArr.indexOf(attr)>=0&&(obj[i][attr]==''||obj[i][attr]==null)){
                                this.$message({
                                    message: '第'+(i+1)+'条商品填写不完整',
                                    type: 'error'
                                });
                                resolve(false)
                                return
                            }
                        }
                    }
                    resolve(true)
                })
            },

            //释放单据
            freeOrder(){
                let self = this;
                self.$confirm('确认释放单据吗', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(({ value }) => {
                    self.$axios.post('/trade/comreceiptorder/freedOrder',[self.ruleForm.orderId]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },

            submitFormData(params,url,status){
                let self = this;
                params.cabinetTypeId = params.cabinetTypeId instanceof Array?params.cabinetTypeId.join(','):  params.cabinetTypeId;
                params.cabinetTypeName = params.cabinetTypeName instanceof Array?params.cabinetTypeName.join(','):  params.cabinetTypeName;
                self.$axios.post(url,params).then((res) => {
                    if(res.code==0){
                        self.$message({
                            message: '操作成功',
                            type: 'success'
                        });
                        if(status==1&&(res.orderId||self.ruleForm.orderId)){
                            self.$router.replace({path: '/home/<USER>/expert',query:{id:res.orderId}})
                            if(res.orderId)self.ruleForm.orderId = res.orderId;
                            self.getDetail();
                        }else{
                            self.back();
                        }
                    }
                })
            },
            //保存
            submitForm(formName,status) {
                let self = this;
                let url = status==2?'/trade/comreceiptorder/saveAndSend':'/trade/comreceiptorder/save';
                let params = self.$util.extend(self.ruleForm);
                let productDetail = [];
                let reg = /[0-9]{10}/;
                for(let i=0;i<params.productDetail.length;i++){
                    let item = params.productDetail[i];
                    if(item.productName!=''&&reg.test(item.hsCode)&&(item.pid||item.id)){
                        productDetail.push(item)
                    }else if(item.productName!=''||item.hsCode!=''){
                        self.$message({
                            message: '第'+(i+1)+'条商品不存在,请重新输入',
                            type: 'error'
                        });
                        return
                    }
                }
                params.productDetail = productDetail;
                if(status==2){
                    self.$refs[formName].validate((valid) => {
                        if (valid) {
                            self.submitFormData(params,url,status)
                            //外贸端暂时不需要校验
                            // self.checkProduct(params.productDetail).then((result)=>{
                            //     if(result){
                            //         self.submitFormData(params,url,status)
                            //     }
                            // })
                        } else {
                            self.$message({
                                message: '请正确填写',
                                type: 'error'
                            });
                            return false;
                        }
                    });
                }else{
                    self.submitFormData(params,url,status)
                }

            },

            //修改订单状态
            submitStatus(formName,status) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        if(!self.ruleForm.orderId){
                            self.$message({
                                message: '请先保存订单!',
                                type: 'error'
                            });
                            return false;
                        }
                        self.$axios.get('/trade/comreceiptorder/chStatus',{params:{orderId:self.ruleForm.orderId,orderStatus:status}}).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.back();
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            //外贸提交过来的单据退回
            rollback(){
                let self = this;
                self.$prompt('请输入退回备注', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputType:'textarea',
                    inputPattern: /\S/,
                    inputErrorMessage: '备注不能为空'
                }).then(({ value }) => {
                    self.$axios.post('/trade/comreceiptorder/rollback',{
                        orderId:self.ruleForm.orderId,
                        remark:value
                    }).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },
            //冻结解冻订单
            frozen(){
                let self = this;
                let str = self.ruleForm.frozen==1?'是否确认解冻订单':'是否确认冻结订单';//是否冻结0否1是
                let frozen = self.ruleForm.frozen==1?'0':'1';
                self.$prompt(str, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /\S/,
                    inputErrorMessage: '请输入备注'
                }).then(({ value }) => {
                    self.$axios.get('/trade/comreceiptorder/frozenConfirm',{
                        params:{
                            orderId:self.ruleForm.orderId,
                            frozen:frozen,
                            remark:value
                        }
                    }).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },
            //修改订单状态
            submitStatus(formName,status) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        if(!self.ruleForm.orderId){
                            self.$message({
                                message: '请先保存订单!',
                                type: 'error'
                            });
                            return false;
                        }
                        self.$axios.get('/trade/comreceiptorder/chStatus',{params:{orderId:self.ruleForm.orderId,orderStatus:status}}).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.back();
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$store.commit('CollapseSidebar',false);//打开侧边栏
                this.$router.replace({path: '/home/<USER>/list'});
            }
        },
        components:{
            vBlock,vSelect,vDialog,vUpload
        }
    }
</script>
<style lang="less">
    .el-form-item{margin-bottom: 5px!important;}
    .red-border{border: 1px solid red;}
    .box-list{
        display: flex;justify-content: space-between;align-items: flex-start;flex-wrap: nowrap;height:40px;margin-top:20px;padding:10px 0;
        .box-int{
            padding-right: 20px;flex-grow:1;
        }
    }
    .h-table{
        tr td{padding: 0;}
        .el-input__inner{padding: 0 5px;border: none;border-radius: 0}
        .cell,th div{padding: 0;}
        .el-table__row{background: rgb(224,240,255)}

        .el-input__suffix{display: none}
    }
</style>
