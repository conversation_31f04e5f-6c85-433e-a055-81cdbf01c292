<template>
    <div>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleDelete('')">删除</el-button>
                <el-button type="primary" @click="handleRead('')">标记已读</el-button>
                <el-button type="primary" @click="handleAllRead">全部已读</el-button>
            </el-row>
            <el-tabs type="card" v-model="params.type" @tab-click="getList">
                <el-tab-pane label="订单消息" name="1"></el-tab-pane>
                <el-tab-pane label="系统消息" name="2"></el-tab-pane>
            </el-tabs>
            <v-table
                :selection="selection"
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :selectionChange="handleSelectionChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>
    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';
    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    type:1,
                    sidx:'',
                    order:'',
                },
                multipleSelection:'',
                total:0,
                totalPage:1,
                tableData: [],
                rowKey:'id',
                selection:true,
                tableHeader: [
                    { type:true},
                    { prop: 'id', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},
                    { prop: 'messageContent', label: '内容',sortable:'custom',message:'isRead'},
                    { prop: 'createTime', label: '提交时间',sortable:'custom'},
                    { prop: 'type', label: '类型',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'delete', clickFun: this.handleDelete },
                            { class: 'zoom-in', clickFun: this.handleDetail }
                        ]
                    },
                ],
                dropTableHeader: [
                    { type:true},
                    { prop: 'id', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},
                    { prop: 'messageContent', label: '内容',sortable:'custom',message:'isRead'},
                    { prop: 'createTime', label: '提交时间',sortable:'custom'},
                    { prop: 'type', label: '类型',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'delete', clickFun: this.handleDelete },
                            { class: 'zoom-in', clickFun: this.handleDetail }
                        ]
                    },
                ],
            }
        },
        mounted(){
            if(this.$route.query.type){
                this.params.type = this.$route.query.type;
            }
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                let params = this.$util.extend(self.params);
                self.$axios.get('/trade/messagemy/list',{params: params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                });
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleDetail(row){
                this.$router.push({path: '/home/<USER>/message/detail',query:{id:row.id}})
            },
            handleRead(row){
                let self = this,ids=[];
                if(!row.id){
                    if(self.multipleSelection==undefined||self.multipleSelection==''){
                        self.$message({type: 'error', message: '请选择需要标记已读的消息'});
                        return false;
                    }
                    self.multipleSelection.map((item)=>{
                        ids.push(item.id)
                    });
                }else{
                    ids = [row.id];
                }
                self.$confirm('是否确认标记已读消息?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/messagemy/updateMarkRead',ids).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            handleAllRead(){
                let self = this
                self.$confirm('是否确认全部已读?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/messagemy/updateAllRead').then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            handleDelete(row){
                let self = this,ids=[];
                if(!row.id){
                    if(self.multipleSelection==undefined||self.multipleSelection==''){
                        self.$message({type: 'error', message: '请选择需要删除的消息'});
                        return false;
                    }
                    self.multipleSelection.map((item)=>{
                        ids.push(item.id)
                    });
                }else{
                    ids = [row.id];
                }
                self.$confirm('是否确认删除消息?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/trade/messagemy/delete',ids).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

