<template>
    <div class="white-bg">
        <div class="btn-bar mb_1">
            详情
            <el-button @click="back()">返回</el-button>
        </div>
        <el-card class="box-card mb_1">
            {{ruleForm.remark}}
        </el-card>
        <el-card class="box-card mb_1">
            <div class="img-box">
                <template v-if="ruleForm.imgUrl.length>0">
                    <img :src="item" alt="" @click="handleImg(index)" v-for="(item,index) in ruleForm.imgUrl">
                </template>
                <template v-else>
                    暂无图片
                </template>
            </div>
        </el-card>
        <el-card class="box-card">
            <p>联系方式：{{ruleForm.contactInfo}}</p>
            <p>提交人：{{ruleForm.userName}}</p>
            <p>提交时间：{{ruleForm.createTime}}</p>
        </el-card>
    </div>
</template>
<script>

    export default {
        data: function () {
            return {
                ruleForm: {
                    id:'',
                    imgUrl:[],
                },
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.id = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let url = '/logistics/jbrsuggestinfo/info/'+this.ruleForm.id
                this.$axios.get(url).then((res) => {
                    if(res.code==0){
                        this.ruleForm = res.jbrSuggestInfo;
                        this.ruleForm.imgUrl = this.ruleForm.imgUrl?this.ruleForm.imgUrl.split(','):[];
                    }
                });
            },
            handleImg(index){
                this.$imgPreview(this.ruleForm.imgUrl,index)
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
<style scoped lang="less">
    .btn-bar{display: flex;justify-content: space-between}
    .img-box{
        display: flex;justify-content: flex-start;flex-wrap: wrap;
        img{width: 200px;height: 200px;margin: 0 20px 20px 0;}
    }
</style>
