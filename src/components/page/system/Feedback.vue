<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="建议反馈">
                        <el-input v-model="params.remark" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="提交时间">
                        <el-date-picker v-model="params.time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    remark:'',
                    time:[],
                },
                count:{},
                rowKey:'orderId',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'createTime', label: '提交时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'remark', label: '建议和反馈',sortable:'custom',minWidth:'200px'},
                    { prop: 'userName', label: '提交人',sortable:'custom',minWidth:'100px'},
                    { prop: 'contactInfo', label: '联系方式',sortable:'custom',minWidth:'100px'},
                    { prop: 'oper', label: '操作', fixed: 'right',width:'140px',
                        oper: [
                            { class: 'zoom-in', clickFun: this.handleDetail,content:'详情'},
                        ]
                    },
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'createTime', label: '提交时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'remark', label: '建议和反馈',sortable:'custom',minWidth:'200px'},
                    { prop: 'userName', label: '提交人',sortable:'custom',minWidth:'100px'},
                    { prop: 'contactInfo', label: '联系方式',sortable:'custom',minWidth:'100px'},
                    { prop: 'oper', label: '操作', fixed: 'right',width:'100px',
                        oper: [
                            { class: 'zoom-in', clickFun: this.handleDetail,content:'详情'},
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                let params = this.$util.extend(self.params);
                try {
                    params.startTime = params.time[0]?params.time[0]:'';
                    params.endTime = params.time[1]?params.time[1]:'';
                }catch (e) {

                }
                delete params.time;
                self.$axios.get('/logistics/jbrsuggestinfo/list',{params: params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },

            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleDetail(row){
                this.$router.push({path: '/home/<USER>/detail',query:{id:row.id}})
            },

        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

