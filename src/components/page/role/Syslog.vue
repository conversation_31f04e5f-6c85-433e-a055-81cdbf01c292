<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="操作人员">
                        <el-input v-model="params.key" placeholder="请输入职员编号或姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="操作时间">
                        <el-date-picker
                            v-model="params.time"
                            value-format="yyyy-MM-dd"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>

                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleDelete('')">删除</el-button>
            </el-row>
            <v-table
                :selection="selection"
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :selectionChange="handleSelectionChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>
    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';
    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    key:'',
                    time:[
                        new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate()),
                        new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate())
                    ],
                    sidx:'',
                    order:'',
                },
                multipleSelection:'',
                total:0,
                totalPage:1,
                tableData: [],
                rowKey:'id',
                selection:true,
                tableHeader: [
                    { type:true},
                    { prop: 'id', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},
                    { prop: 'loginUserName', label: '操作者',sortable:'custom'},
                    { prop: 'loginUserName', label: '登录账号',sortable:'custom'},
                    { prop: 'createTime', label: '操作日期',sortable:'custom'},
                    { prop: 'ipAddress', label: 'IP地址',sortable:'custom'},
                    { prop: 'remark', label: '操作记录',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { type:true},
                    { prop: 'id', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},
                    { prop: 'loginUserName', label: '操作者',sortable:'custom'},
                    { prop: 'loginUserName', label: '登录账号',sortable:'custom'},
                    { prop: 'createTime', label: '操作日期',sortable:'custom'},
                    { prop: 'ipAddress', label: 'IP地址',sortable:'custom'},
                    { prop: 'remark', label: '操作记录',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                let params = this.$util.extend(self.params);
                try {
                    params.startTime = params.time[0]?params.time[0]:'';
                    params.endTime = params.time[1]?params.time[1]:'';
                }catch (e) {

                }
                delete params.time;
                self.$axios.get('/base/logmy/list',{params: params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                });
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleDelete(row){
                let self = this,ids=[];
                if(!row.id){
                    if(self.multipleSelection==undefined||self.multipleSelection==''){
                        self.$message({type: 'error', message: '请选择需要删除的日志'});
                        return false;
                    }
                    self.multipleSelection.map((item)=>{
                        ids.push(item.id)
                    });
                    ids = ids.join(",");
                }else{
                    ids = row.id;
                }
                self.$confirm('是否确认删除日志?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/base/logmy/delete',[ids]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

