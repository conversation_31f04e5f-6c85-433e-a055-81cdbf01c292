<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.userId">编辑</span><span v-else>新建</span>职员</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="职员编号:" prop="username">
                   <span style="width: 45px;display: inline-block" v-if="!ruleForm.userId">{{companyCode}}+</span>
                    <el-input style="max-width: 255px" disabled v-if="ruleForm.userId" v-model="ruleForm.username"></el-input>
                    <el-input style="max-width: 255px" v-else v-model="ruleForm.username"></el-input>
                </el-form-item>
                <el-form-item label="职员姓名:" prop="nickName">
                    <el-input v-model="ruleForm.nickName"></el-input>
                </el-form-item>
                <el-form-item label="所属角色:" prop="roleId">
                    <el-select v-model="ruleForm.roleId" placeholder="请选择角色">
                        <el-option
                            v-for="item in role"
                            :key="item.roleId"
                            :label="item.roleName"
                            :value="item.roleId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="登录密码:" prop="password">
                    <el-input v-model="ruleForm.password"></el-input>
                </el-form-item>
                <el-form-item label="账号状态:" prop="status">
                    <el-radio v-model="ruleForm.status" label="1">正常</el-radio>
                    <el-radio v-model="ruleForm.status" label="0">冻结</el-radio>
                </el-form-item>
                <el-form-item label="职员邮箱:" prop="email">
                    <el-input v-model="ruleForm.email"></el-input>
                </el-form-item>
                <el-form-item label="职员手机号:" prop="mobile">
                    <el-input v-model="ruleForm.mobile"></el-input>
                </el-form-item>
                <el-form-item label="备注:" prop="remark">
                    <el-input v-model="ruleForm.remark"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>

    export default {
        data: function () {
            return {
                ruleForm: {
                    userId:'',
                    username: '',
                    nickName:'',
                    password: '',
                    email: '',
                    mobile: '',
                    remark: '',
                    status: '1',
                    roleId: ''
                },
                role:[],
                companyCode:'',
                defaultProps: {
                    children: 'childList',
                    label: 'name'
                },
                rules: {
                    username: [
                        { required: true, message: '请输入职员编号', trigger: 'blur' },
                    ],
                    nickName: [
                        { required: true, message: '请填写职员姓名', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入登录密码', trigger: 'blur' },
                        { pattern:/^(\w){6,20}$/, message: '只能输入6-20个字母、数字、下划线', trigger: 'blur' }
                    ],
                    roleId: [
                        { required: true, message: '请选择所属角色', trigger: 'change' }
                    ],
                    email: [
                        { pattern:/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/, message: '请输入正确的邮箱地址', trigger: 'blur' }
                    ],
                    mobile: [
                        { required: true, message: '请输入手机号', trigger: 'blur' },
                        { pattern:/^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                    ],
                    status: [
                        { required: true, message: '请选择账号状态', trigger: 'change' }
                    ],

                },
            }
        },
        mounted(){
            this.getRole();
            this.getCompanyCode();
            if(this.$route.query.id){
                this.ruleForm.userId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getRole(){
                let self = this;
                self.$axios.get('/sys/role/select').then((res) => {
                    if(res.code==0){
                        self.role=res.list;
                    }
                });
            },
            getCompanyCode(){
                let self = this;
                self.$axios.get('/sys/user/info').then((res) => {
                    if(res.code==0){
                        self.companyCode = res.user.companyCode
                    }
                });
            },
            getDetail(){
                let self = this,url = '/sys/user/info/'+self.ruleForm.userId;
                self.$axios.get(url).then((res) => {
                    let user = res.user
                    if(res.code==0){
                        self.ruleForm = {
                                userId:user.userId,
                                username: user.username,
                                nickName: user.nickName,
                                password: user.password,
                                email: user.email,
                                mobile: user.mobile,
                                remark: user.remark,
                                status: ''+user.status,
                                roleId: user.roleId
                        };
                    }
                });
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.userId?'/sys/user/update':'/sys/user/save';
                        let params = {}
                        self.$util.merge(params,self.ruleForm);
                        params.username = params.userId?self.ruleForm.username:self.companyCode+self.ruleForm.username;
                        self.$axios.post(url,params).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
