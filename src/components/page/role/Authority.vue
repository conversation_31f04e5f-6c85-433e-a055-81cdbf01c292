<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="角色名称">
                        <el-input v-model="params.roleName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    roleName:'',
                    sidx:'',
                    order:'',
                },
                rowKey:'roleId',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { prop: 'roleId', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'roleSerial', label: '角色编号',sortable:'custom'},
                    { prop: 'roleName', label: '角色名称',sortable:'custom'},
                    { prop: 'remark', label: '描述',sortable:'custom'},
                    { prop: 'msgStatus', label: '一般贸易',sortable:'custom',formatData: function(val) { return val == 0 ? '自己' :val == 1 ? '所有' : '未知'; }},
                    { prop: 'roleNumber', label: '人数',sortable:'custom'},
                    { prop: 'createTime', label: '修改时间',sortable:'custom'},
                    { prop: 'roleStatus', label: '状态',sortable:'custom', formatData: function(val) { return val == 0 ? '已停用' :val == 1 ? '已开启' : '未知'; } },
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { prop: 'roleId', label: '序号',sortable:'custom', fixed: 'left',width:"70px"},

                    { prop: 'roleSerial', label: '角色编号',sortable:'custom'},
                    { prop: 'roleName', label: '角色名称',sortable:'custom'},
                    { prop: 'remark', label: '描述',sortable:'custom'},
                    { prop: 'msgStatus', label: '一般贸易',sortable:'custom',formatData: function(val) { return val == 0 ? '自己' :val == 1 ? '所有' : '未知'; }},
                    { prop: 'roleNumber', label: '人数',sortable:'custom'},
                    { prop: 'createTime', label: '修改时间',sortable:'custom'},
                    { prop: 'roleStatus', label: '状态',sortable:'custom', formatData: function(val) { return val == 0 ? '已停用' :val == 1 ? '已开启' : '未知'; } },
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/sys/role/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/authority/edit',query:{id:row.roleId}})
            },
            handleDelete(row){
                let self = this;
                if(row.roleNumber>0){
                    self.$message({
                        type: 'error',
                        message: '当前角色下存在用户，不能删除!'
                    });
                    return
                }
                self.$confirm('是否确认删除该角色?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/sys/role/delete',[row.roleId]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

