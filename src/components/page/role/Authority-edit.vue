<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.roleId">编辑</span><span v-else>新建</span>角色</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="角色编号:" prop="roleSerial">
                    <el-input v-model="ruleForm.roleSerial"></el-input>
                </el-form-item>
                <el-form-item label="角色名称:" prop="roleName">
                    <el-input v-model="ruleForm.roleName"></el-input>
                </el-form-item>
                <el-form-item label="角色描述:" prop="remark">
                    <el-input v-model="ruleForm.remark"></el-input>
                </el-form-item>
                <el-form-item label="权限资源:" prop="menuIdList">
                    <el-tree :data="menus"
                             show-checkbox
                             accordion
                             node-key="menuId"
                             ref="tree"
                             default-expand-all
                             highlight-current
                             @check-change="checkChange"
                             :props="defaultProps">
                    </el-tree>
                </el-form-item>
                <el-form-item label="一般贸易:" prop="msgStatus">
                    <el-radio v-model="ruleForm.msgStatus" label="0">所有单据</el-radio>
                    <el-radio v-model="ruleForm.msgStatus" label="1">仅自己</el-radio>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>

    export default {
        data: function () {
            var checkMenu = (rule, value, callback) => {
                if (value.length==0) {
                    return callback(new Error('请选择权限'));
                }else {
                    callback();
                }
            };
            return {
                ruleForm: {
                    roleId:'',
                    roleSerial: '',
                    roleName: '',
                    remark: '',
                    msgStatus: '0',
                    menuIdList: [],
                    subMenuIdList: [],
                },
                menus:[],
                defaultProps: {
                    children: 'childList',
                    label: 'name'
                },
                rules: {
                    roleSerial: [
                        { required: true, message: '请填写角色编号', trigger: 'blur' }
                    ],
                    roleName: [
                        { required: true, message: '请填写角色名称', trigger: 'blur' }
                    ],
                    menuIdList: [
                        {required: true, validator: checkMenu, trigger: 'change' }
                    ],
                    msgStatus: [
                        { required: true, message: '请选择一般贸易', trigger: 'change' }
                    ],
                },
            }
        },
        mounted(){
            this.getMenus();
            if(this.$route.query.id){
                this.ruleForm.roleId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getMenus(){
                let self = this;
                self.$axios.get('/sys/menu/roleMenus').then((res) => {
                    if(res.code==0){
                        self.menus = res.roleMenus;
                    }
                });
            },
            getDetail(){
                let self = this,url = '/sys/role/info/'+self.ruleForm.roleId;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        self.ruleForm={
                                roleId:res.role.roleId,
                                roleSerial: res.role.roleSerial,
                                roleName: res.role.roleName,
                                remark: res.role.remark,
                                msgStatus: ''+res.role.msgStatus,
                                menuIdList: res.role.menuIdList,
                                subMenuIdList: res.role.subMenuIdList,
                        };
                        self.$refs.tree.setCheckedKeys(res.role.menuIdList);
                    }
                });
            },
            checkChange(){
                this.ruleForm.menuIdList = this.$refs.tree.getCheckedKeys();
                this.ruleForm.subMenuIdList = this.$refs.tree.getHalfCheckedKeys();
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.roleId?'/sys/role/update':'/sys/role/save';
                        self.$axios.post(url,self.ruleForm).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
