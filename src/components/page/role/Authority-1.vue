<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="角色名称">
                        <el-input v-model="params.roleName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <el-table :data="tableData" row-key="roleId" border @sort-change="sortChange" style="width: 100%">
                <el-table-column sortable='custom' width="70" prop="roleId" label="序号" align="center">
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <a class="h-btn" @click="handleEdit(scope.row.roleId)"><i class="el-icon-edit"></i></a>
                        <a class="h-btn" @click="handleDelete(scope.row.roleId)"><i class="el-icon-delete"></i></a>
                    </template>
                </el-table-column>
                <el-table-column sortable='custom' prop="roleSerial" label="角色编号" align="center">
                </el-table-column>
                <el-table-column sortable='custom' prop="roleName" label="角色名称" align="center">
                </el-table-column>
                <el-table-column sortable='custom' prop="remark" label="描述" align="center">
                </el-table-column>
                <el-table-column sortable='custom' prop="createTime" label="一般贸易" align="center">
                </el-table-column>
                <el-table-column sortable='custom' prop="roleNumber" label="人数" align="center">
                </el-table-column>
                <el-table-column sortable='custom' prop="createTime" label="修改时间" align="center">
                </el-table-column>
                <el-table-column sortable='custom' prop="roleStatus" label="状态" align="center" :formatter="formatStatus">
                </el-table-column>


            </el-table>
        </div>
        <div class="text-center mt_2">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="params.page"
                :page-sizes="[50, 100, 300, 500]"
                :page-size="params.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
    import vSearch from './../public/Search';
    export default {
        data() {
            return {
                params:{
                    page:1,
                    size:50,
                    roleName:'',
                    sidx:'',
                    order:'',
                },
                total:0,
                totalPage:1,
                tableData: []
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/sys/role/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                });
            },
            handleSizeChange(val) {
                this.params.size = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='size'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(id){
                this.$router.push({path: '/home/<USER>/authority/edit',query:{id:id}})
            },
            handleDelete(id){
                let self = this;
                self.$confirm('是否确认删除角色?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/sys/role/delete',[id]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: res.msg
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch
        }
    }
</script>

