<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="运单号">
                        <el-input v-model="params.trackingNum" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="境内收发货人" label-width="100px">
                        <el-input v-model="params.jnNickName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="发货时间">
                        <el-date-picker v-model="params.time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </template>
            <template slot="form2">
                <el-col :span="6">
                    <el-form-item label="转运单号">
                        <el-input v-model="params.transhiNum" placeholder="请输入内容"></el-input>
                    </el-form-item>
<!--                    <el-form-item label="发货渠道">-->
<!--                        <channel v-model="params.sendChannel" @change="channel=>{params.sendChannel=channel[0]}" :id="params.sendChannel"></channel>-->

<!--                        &lt;!&ndash;                        <el-input v-model="params.sendChannelName" placeholder="请输入内容"></el-input>&ndash;&gt;-->
<!--                    </el-form-item>-->
                </el-col>
                <el-col :span="6">
                    <el-form-item label="境外收发货人" label-width="100px">
                        <el-input v-model="params.jwNickName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="运单状态">
                        <el-select v-model="params.orderStatus" placeholder="请选择运单状态">
                            <el-option
                                v-for="item in STATUS.orderStatus"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <div class="h-tab">
                <div class="h-tab-list">
                    <a :class="{'cur':params.orderStatus==100}" @click="params.orderStatus=100;getList()">待发货({{count.waitSendCount}})</a>
                    <a :class="{'cur':params.orderStatus==200}" @click="params.orderStatus=200;getList()">待收货({{count.waitPushCount}})</a>
                    <a :class="{'cur':params.orderStatus==400}" @click="params.orderStatus=400;getList()">已收货({{count.aleryPushCount}})</a>
                    <a :class="{'cur':params.orderStatus==1000}" @click="params.orderStatus=1000;getList()">已删除({{count.deleCount}})</a>
                    <a :class="{'cur':params.orderStatus==500}" @click="params.orderStatus=500;getList()">已退回({{count.rollbackCount}})</a>
                    <a :class="{'cur':params.orderStatus==600}" @click="params.orderStatus=600;getList()">问题件({{count.problemCount}})</a>
                    <a :class="{'cur':params.orderStatus==''}" @click="params.orderStatus='';getList()">全部</a>
                </div>
            </div>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    trackingNum:'',
                    // transhiNum:'',
                    sendChannel:'',
                    jnNickName:'',
                    jwNickName:'',
                    orderStatus:'',
                    time:[],
                },
                count:{},
                rowKey:'orderId',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'companyName', label: '公司名称',sortable:'custom',minWidth:'140px'},
                    { prop: 'companyCode', label: '公司编号',sortable:'custom',minWidth:'140px'},
                    { prop: 'orderNum', label: '统一编号',sortable:'custom',minWidth:'100px'},
                    { prop: 'sendType', label: '发货类型',sortable:'custom',minWidth:'100px',formatData: this.FILTER.sendType},
                    { prop: 'sendChannelName', label: '发货渠道',sortable:'custom',minWidth:'100px'},
                    { prop: 'sendTime', label: '发货时间',sortable:'custom',minWidth:'100px'},
                    { prop: 'trackings', label: '运单号',sortable:'custom',minWidth:'100px'},
                    { prop: 'transhis', label: '转运单号',sortable:'custom',minWidth:'100px'},
                    { prop: 'sendNickName', label: '发货人姓名',sortable:'custom',minWidth:'120px'},
                    { prop: 'sendMobile', label: '手机号',sortable:'custom',minWidth:'100px'},
                    { prop: 'putNickName', label: '收货人姓名',sortable:'custom',minWidth:'120px'},
                    { prop: 'putMobile', label: '手机号',sortable:'custom',minWidth:'100px'},
                    { prop: 'putCountry', label: '收货国家',sortable:'custom',minWidth:'100px'},
                    { prop: 'productType', label: '货物类型',sortable:'custom',minWidth:'100px',formatData: this.FILTER.productType},
                    { prop: 'baoQingGuan', label: '包清关',sortable:'custom',minWidth:'90px',formatData: this.FILTER.baoQingGuan},
                    { prop: 'charWeightKg', label: '重量',sortable:'custom',minWidth:'80px'},
                    { prop: 'freightMoneyCount', label: '运费',sortable:'custom',minWidth:'80px'},
                    { prop: 'orderStatus', label: '单据状态',sortable:'custom',minWidth:'100px',formatData: this.FILTER.orderStatus},
                    { prop: 'createUserName', label: '创建人',sortable:'custom',minWidth:'100px'},
                    { prop: 'createTime', label: '创建时间',sortable:'custom',minWidth:'100px'},
                    { prop: 'updateUserName', label: '修改人',sortable:'custom',minWidth:'100px'},
                    { prop: 'updateTime', label: '修改时间',sortable:'custom',minWidth:'100px'},
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'companyName', label: '公司名称',sortable:'custom',minWidth:'140px'},
                    { prop: 'companyCode', label: '公司编号',sortable:'custom',minWidth:'140px'},
                    { prop: 'orderNum', label: '统一编号',sortable:'custom',minWidth:'100px'},
                    { prop: 'sendType', label: '发货类型',sortable:'custom',minWidth:'100px',formatData: this.FILTER.sendType},
                    { prop: 'sendChannelName', label: '发货渠道',sortable:'custom',minWidth:'100px'},
                    { prop: 'sendTime', label: '发货时间',sortable:'custom',minWidth:'100px'},
                    { prop: 'trackings', label: '运单号',sortable:'custom',minWidth:'100px'},
                    { prop: 'transhis', label: '转运单号',sortable:'custom',minWidth:'100px'},
                    { prop: 'sendNickName', label: '发货人姓名',sortable:'custom',minWidth:'120px'},
                    { prop: 'sendMobile', label: '手机号',sortable:'custom',minWidth:'100px'},
                    { prop: 'putNickName', label: '收货人姓名',sortable:'custom',minWidth:'120px'},
                    { prop: 'putMobile', label: '手机号',sortable:'custom',minWidth:'100px'},
                    { prop: 'putCountry', label: '收货国家',sortable:'custom',minWidth:'100px'},
                    { prop: 'productType', label: '货物类型',sortable:'custom',minWidth:'100px',formatData: this.FILTER.productType},
                    { prop: 'baoQingGuan', label: '包清关',sortable:'custom',minWidth:'90px',formatData: this.FILTER.baoQingGuan},
                    { prop: 'charWeightKg', label: '重量',sortable:'custom',minWidth:'80px'},
                    { prop: 'freightMoneyCount', label: '运费',sortable:'custom',minWidth:'80px'},
                    { prop: 'orderStatus', label: '单据状态',sortable:'custom',minWidth:'100px',formatData: this.FILTER.orderStatus},
                    { prop: 'createUserName', label: '创建人',sortable:'custom',minWidth:'100px'},
                    { prop: 'createTime', label: '创建时间',sortable:'custom',minWidth:'100px'},
                    { prop: 'updateUserName', label: '修改人',sortable:'custom',minWidth:'100px'},
                    { prop: 'updateTime', label: '修改时间',sortable:'custom',minWidth:'100px'},
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
            this.getCount();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                let params = this.$util.extend(self.params);
                try {
                    params.startTime = params.time[0]?params.time[0]:'';
                    params.endTime = params.time[1]?params.time[1]:'';
                }catch (e) {

                }
                delete params.time;
                this.getCount();
                self.$axios.get('/logistics/jbrorderinfo/list',{params: params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            getCount(){
                this.$axios.get('/logistics/jbrorderinfo/count').then((res) => {
                    this.count = {
                        aleryPushCount: res.aleryPushCount,
                        code: res.code,
                        daiHuiLuCount: res.daiHuiLuCount,
                        deleCount: res.deleCount,
                        problemCount: res.problemCount,
                        rollbackCount: res.rollbackCount,
                        waitPushCount: res.waitPushCount,
                        waitSendCount: res.waitSendCount
                    }
                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/edit',query:{id:row.orderId}})
            },
            handleDetail(row){
                this.$router.push({path: '/home/<USER>/detail',query:{id:row.orderId}})
            },

        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

