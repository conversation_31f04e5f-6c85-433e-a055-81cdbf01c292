<template>
    <div class="white-bg">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" size="medium" label-width="100px" class="demo-ruleForm">
            <el-form-item prop="type" label="发送对象：">
                <el-select class="z-100003" :popper-append-to-body="false" v-model="ruleForm.type" placeholder="请选择发送对象">
                    <el-option
                        v-for="item in STATUS.messageType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="messageTitle" label="消息标题：">
                <el-input
                    type="text"
                    placeholder="请输入消息标题"
                    v-model="ruleForm.messageTitle"
                    maxlength="30"
                    show-word-limit
                ></el-input>
<!--                <el-input v-model="ruleForm.messageTitle" maxlength="30" @input="descInput" placeholder="请输入消息标题(1-60个字节)"></el-input>-->
<!--                <span class="h-limit">{{remnant}}/60</span>-->
            </el-form-item>
            <el-form-item prop="messageContent" label="消息内容：">
                <editor-bar v-model="ruleForm.messageContent" :isClear="isClear"></editor-bar>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" @click="submitForm('ruleForm',1)">发布</el-button>
            <el-button @click="back">返回</el-button>
        </div>
    </div>
</template>
<script>
    import EditorBar from './../../common/Editor'
    export default {
        data: function () {
            let validateLength = (rule, value, callback) => {
                let len=0;
                for (let i=0; i<value.length; i++) {
                    let c = value.charCodeAt(i);
                    //单字节加1
                    if (c>127 || c==94) {
                        len += 2;
                    } else {
                        len ++;
                    }
                }
                if (len<1||len>60) {
                    callback(new Error('标题字数限制在1-60个字节(中文占两个)'));
                } else {
                    callback();
                }
            };
            return {
                isClear:false,
                loading:'',
                remnant:0,
                ruleForm: {
                    messageContent:'',//富文本
                    messageTitle:'',
                    type:'',
                    isRead:0,
                    havaSend:0,
                },
                rules: {
                    type: [
                        { required: true, message: '请选择发送对象', trigger: 'change' }
                    ],
                    messageTitle: [
                        { required: true, message: '请输入消息标题', trigger: 'blur' }
                    ],
                    messageContent: [
                        { required: true, message: '请输入消息内容', trigger: 'blur' }
                    ],
                }
            }
        },
        mounted(){
            this.ruleForm.id = this.$route.query.id?this.$route.query.id:'';
            if(this.ruleForm.id){
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let self = this;
                self.$axios.get('/logistics/messagemg/info/'+self.ruleForm.id).then((res) => {
                    if(res.code==0){
                       self.ruleForm = res.messageMg;
                    }
                })
            },
            // descInput() {
            //     let len=0,str = this.ruleForm.messageTitle;
            //     for (let i=0; i<str.length; i++) {
            //         let c = str.charCodeAt(i);
            //         //单字节加1
            //         if (c>127 || c==94) {
            //             len += 2;
            //         } else {
            //             len ++;
            //         }
            //     }
            //     this.remnant = len;
            // },
            submitForm(formName,status) {
                let self = this;

                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.id?'/logistics/messagemg/update':'/logistics/messagemg/save';
                        let params = self.$util.extend(self.ruleForm);
                        self.$axios.post(url,params).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                this.$router.push({path: '/home/<USER>/list'})
                                // self.ruleForm={
                                //     messageContent:'',
                                //     messageTitle:'',
                                // };
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        },
        components:{
            EditorBar
        }
    }
</script>
<style>
    .z-100003 .el-select-dropdown.el-popper{
        z-index: 100003!important;
    }
    .el-input .el-input__count .el-input__count-inner{
        background: transparent;
    }
</style>
