<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="标题">
                        <el-input v-model="params.messageTitle" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="发布时间">
                        <el-date-picker v-model="params.time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    messageTitle:'',
                    time:[
                        // new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate()),
                        // new Date().getFullYear()+'-'+(new Date().getMonth()+1<10?('0'+(new Date().getMonth()+1)):(new Date().getMonth()+1))+'-'+(new Date().getDate()<10?('0'+new Date().getDate()):new Date().getDate())
                    ],
                },
                rowKey:'id',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'messageTitle', label: '标题内容',sortable:'custom',minWidth:'300px',message:'isRead'},
                    { prop: 'userName', label: '发布人',sortable:'custom',minWidth:'140px'},
                    { prop: 'type', label: '发布对象',sortable:'custom',minWidth:'140px',formatData: this.FILTER.messageType},
                    { prop: 'createTime', label: '发布时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'oper', label: '操作', fixed: 'right',minWidth:'140px',
                        oper: [
                            { class: 'edit',statusKey:'havaSend',statusVal:['0'], clickFun: this.handleEdit,content:'编辑'},
                            { class: 'delete',statusKey:'havaSend',statusVal:['0'], clickFun: this.handleDelete,content:'删除'},
                            { class: 's-promotion',statusKey:'havaSend',statusVal:['0'], clickFun: this.handleSend,content:'发送'},
                            { class: 'folder-delete',statusKey:'havaSend',statusVal:['1'], clickFun: this.handleSend,content:'撤回'}
                        ]
                    },

                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'messageTitle', label: '标题内容',sortable:'custom',minWidth:'300px',message:'isRead'},
                    { prop: 'userName', label: '发布人',sortable:'custom',minWidth:'140px'},
                    { prop: 'type', label: '发布对象',sortable:'custom',minWidth:'140px',formatData: this.FILTER.messageType},
                    { prop: 'createTime', label: '发布时间',sortable:'custom',minWidth:'140px'},
                    { prop: 'oper', label: '操作', fixed: 'right',minWidth:'140px',
                        oper: [
                            { class: 'edit',statusKey:'havaSend',statusVal:['0'], clickFun: this.handleEdit,content:'编辑'},
                            { class: 'delete',statusKey:'havaSend',statusVal:['0'], clickFun: this.handleDelete,content:'删除'},
                            { class: 's-promotion',statusKey:'havaSend',statusVal:['0'], clickFun: this.handleSend,content:'发送'},
                            { class: 'folder-delete',statusKey:'havaSend',statusVal:['1'], clickFun: this.handleSend,content:'撤回'}
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                let params = this.$util.extend(self.params);
                try {
                    params.startTime = params.time[0]?params.time[0]:'';
                    params.endTime = params.time[1]?params.time[1]:'';
                }catch (e) {

                }
                delete params.time;
                self.$axios.get('/logistics/messagemg/list',{params: params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/edit',query:{id:row.id}})
            },
            handleDelete(row){
                let self = this;
                self.$confirm('是否确认删除该消息?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/logistics/messagemg/delete',[row.id]).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            handleSend(row){
                let url = row.havaSend==1?'/logistics/messagemg/rollMessage/':'/logistics/messagemg/sendMessage/'
                url+=row.id;
                let msg = row.havaSend==1?'是否确认撤回':'是否确认发送'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$axios.post(url).then((res) => {
                        if(res.code==0){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList();
                        }
                    });
                }).catch(() => {

                });

            }
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

