<template>
    <div class="white-bg">
        <div class="btn-bar mb_1">
            消息详情
            <el-button @click="back()">返回</el-button>
        </div>
        <div class="message-title">
            <h3 class="text-center mb_1">{{ruleForm.messageTitle}}</h3>
            <p class="text-center">{{ruleForm.createTime}}</p>
        </div>

        <div class="message-cont" @click="handleMessageRoute">
            {{ruleForm.messageContent}}
        </div>
    </div>
</template>
<script>

    export default {
        data: function () {
            return {
                ruleForm: {
                    id:'',
                },
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.id = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let self = this;
                let url = '/logistics/messagemg/info/'+self.ruleForm.id
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        debugger
                        self.ruleForm = res.messageMg
                    }
                });
            },
            handleMessageRoute(){
                let path,orderType = this.ruleForm.orderType,orderStatus=this.ruleForm.orderStatus
                if(orderStatus==500){
                    path = orderType==1?'/home/<USER>/commonConfirm':'/home/<USER>/purchaseConfirm'
                }else if(orderStatus==600){
                    path = orderType==1?'/home/<USER>/commonDeclared':'/home/<USER>/purchaseDeclared';
                }else if(orderStatus>600){
                    path = orderType==1?'/home/<USER>/commonDetail':'/home/<USER>/purchaseDetail';
                }
                if(path)this.$router.push({path: path,query:{id:this.ruleForm.orderId}})
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
<style scoped lang="less">
    .btn-bar{display: flex;justify-content: space-between}
    .message-title{
        border: 1px solid #dcdfe6;border-bottom: none;padding: 30px;
        h3{}
        p{color: #666;font-size: 12px}
    }
    .message-cont{border: 1px solid #dcdfe6;padding:20px}
</style>
