<template>
    <el-dialog @close="closeDialog" :width="width" :fullscreen="fullscreen" :top="top" custom-class="h-dialog" :title="title" :visible.sync="dialogFromVisible">
        <slot name="dialog"></slot>
        <div slot="footer" class="dialog-footer text-center">
            <el-button @click="cancelDialog">取 消</el-button>
            <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    export default {
        data() {
            return {
                dialogFromVisible:false,
            }
        },
        props:{
            dialogVisible:{
                type: Boolean,
                default: false
            },
            width:{
                type: String,
                default: '50%'
            },
            fullscreen:{
                type: Boolean,
                default: false
            },
            top:{
                type: String,
                default: '15vh'
            },
            title:{
                type: String,
                default: ''
            },
            cancelDialog: {
                type: Function,
                default:function () {
                    this.dialogVisible = false
                }
            },
            enterDialog: {
                type: Function,
                default:function () {
                    this.dialogVisible = false
                }
            },
        },
        watch:{
            dialogVisible(newValue,oldValue){
                this.dialogFromVisible = newValue;
            }
        },
        methods:{
            closeDialog(){
                this.$emit('dialogStatus',this.dialogFromVisible)
            }
        }
    }
</script>
