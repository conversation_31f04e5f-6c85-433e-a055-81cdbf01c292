<template>
    <div class="block-section">
        <p class="block-section-title">
            <i :class="[defaultBlockVisible?'el-icon-remove-outline':'el-icon-circle-plus-outline']" @click="collapse"></i>
            {{title}}
        </p>
        <div class="block-section-content" v-show="defaultBlockVisible">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script>
    export default {
        props:{
            blockVisible:{
                type: Boolean,
                default: true
            },
            title:{
                type: String,
                default: ''
            }
        },
        data(){
            return{
                defaultBlockVisible:''
            }
        },
        mounted(){
            this.defaultBlockVisible = this.blockVisible;
        },
        methods:{
            collapse(){
                this.defaultBlockVisible = !this.defaultBlockVisible;
            }
        }
    }
</script>
