<template>
    <div style="overflow: hidden;">
        <el-row :gutter="20" class="search-box">
            <el-form :label-position="labelPosition" :label-width="labelWidth" label-suffix=":">
                <slot name="form1" class="cl"></slot>
                <slot name="form2" v-if="search"></slot>
                <el-col :span="6">
                    <el-form-item label-width="0px">
                        <el-button type="primary" @click="getList">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                        <a class="toggle-btn" @click="toggle">{{search?'收起':'展开'}}<i :class="search ? 'el-icon-arrow-up' : 'el-icon-arrow-down' "></i></a>
                    </el-form-item>
                </el-col>

            </el-form>
        </el-row>
        <div class="separation-bar"></div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                search:true,
                labelPosition: 'left',
            }
        },
        props: {
            searchFun: Function,
            labelWidth:{
                type:String,
                default:'80px'
            },
            resetFun: Function,
        },
        methods: {
            getList(){
                this.searchFun();
            },
            resetSearch(){
                this.resetFun()
            },
            toggle(){
                this.search = !this.search;
            }
        }
    }
</script>

<style scoped>

</style>
