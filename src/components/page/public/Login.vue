<template>
  <div class="login-wrap" :class="{ active: forget }">
    <div class="ms-title">浙江尼豪供应链</div>
    <div class="ms-login">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="0px"
        class="demo-ruleForm"
        v-if="!forget"
      >
        <el-form-item prop="username">
          <el-input v-model="ruleForm.username" placeholder="用户名"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            type="password"
            placeholder="密码"
            v-model="ruleForm.password"
            @keyup.enter.native="submitForm('ruleForm')"
          ></el-input>
        </el-form-item>
        <div class="login-btn">
          <el-button type="primary" @click="submitForm('ruleForm')"
            >登录</el-button
          >
          <!--<el-button type="danger" @click="forget=true">忘记密码</el-button>-->
        </div>
      </el-form>
      <el-form
        :model="forgetForm"
        :rules="forgetRules"
        ref="forgetForm"
        label-width="0px"
        class="demo-ruleForm"
        v-if="forget"
      >
        <el-form-item prop="telephone">
          <el-input
            v-model="forgetForm.telephone"
            placeholder="手机号"
          ></el-input>
        </el-form-item>
        <el-form-item prop="smsCode">
          <el-row :gutter="20">
            <el-col :span="16">
              <el-input
                placeholder="短信验证码"
                v-model="forgetForm.smsCode"
              ></el-input>
            </el-col>
            <el-col :span="8" class="text-right">
              <el-button type="danger" v-if="show" class="yzm" @click="getCode"
                >获取验证码</el-button
              >
              <el-button type="info" v-if="!show" class="yzm"
                >{{ count }} s</el-button
              >
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item prop="newPwd">
          <el-input
            type="password"
            placeholder="设置新密码"
            auto-complete="off"
            v-model="forgetForm.newPwd"
          ></el-input>
        </el-form-item>
        <el-form-item prop="newPwdAgain">
          <el-input
            type="password"
            placeholder="再次输入新密码"
            auto-complete="off"
            v-model="forgetForm.newPwdAgain"
            @keyup.enter.native="forgetPwd('forgetForm')"
          ></el-input>
        </el-form-item>
        <div class="login-btn">
          <el-button type="primary" @click="forgetPwd('forgetForm')"
            >重置密码</el-button
          >
          <el-button type="danger" @click="forget = false">去登录</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  data: function () {
    var validatePass = (rule, value, callback) => {
      const reg = /^(\w){6,20}$/;
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (!reg.test(value)) {
        callback(new Error("只能输入6-20个字母、数字、下划线"));
      } else {
        if (this.forgetForm.newPwdAgain !== "") {
          this.$refs.forgetForm.validateField("newPwdAgain");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.forgetForm.newPwd) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        username: "",
        password: "",
      },
      forgetForm: {
        telephone: "",
        smsCode: "",
        newPwd: "",
        newPwdAgain: "",
        messageId: "",
      },
      forgetRules: {
        telephone: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        smsCode: [
          { required: true, message: "请输入短信验证码", trigger: "blur" },
        ],
        newPwd: [{ validator: validatePass, trigger: "blur" }],
        newPwdAgain: [{ validator: validatePass2, trigger: "blur" }],
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      forget: false,
      show: true,
      count: "",
      timer: null,
    };
  },
  beforeCreate() {
    let token = localStorage.getItem("adminToken");
    return token ? this.$router.replace("/home/<USER>/list") : "";
  },
  methods: {
    submitForm(formName) {
      const self = this;
      self.$refs[formName].validate((valid) => {
        if (valid) {
          self.$axios
            .post("/sys/login", {
              username: this.ruleForm.username,
              password: this.ruleForm.password,
            })
            .then((res) => {
              if (res.code == 0) {
                let router;
                router = "/home/<USER>/list";
                localStorage.setItem("adminToken", res.token);
                self.$router.replace(router);
              }
            });
        } else {
          return false;
        }
      });
    },
    getCode() {
      const self = this;
      let myreg = /^1[3-9]\d{9}$/;
      if (!myreg.test(self.forgetForm.telephone)) {
        self.$message({
          message: "请输入正确的手机号码",
          type: "error",
        });
        return false;
      }
      self.$axios
        .get("/admin/common/sendValidationCode", {
          params: { telephone: self.forgetForm.telephone },
        })
        .then((res) => {
          if (res.status == 1) {
            self.forgetForm.messageId = res.data.messageId;
            const TIME_COUNT = 60;
            if (!this.timer) {
              this.count = TIME_COUNT;
              this.show = false;
              this.timer = setInterval(() => {
                if (this.count > 0 && this.count <= TIME_COUNT) {
                  this.count--;
                } else {
                  this.show = true;
                  clearInterval(this.timer);
                  this.timer = null;
                }
              }, 1000);
            }
          }
        });
    },
    forgetPwd(formName) {
      const self = this;
      self.$refs[formName].validate((valid) => {
        if (valid) {
          self.$axios
            .get("/admin/menus/forgetPwd", { params: self.forgetForm })
            .then((res) => {
              if (res.status == 1) {
                self.$message({
                  message: "密码重置成功",
                  type: "success",
                });
                self.forget = false;
              }
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less" ref="stylesheet/less">
.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}
.ms-title {
  position: absolute;
  top: 50%;
  width: 100%;
  margin-top: -160px;
  text-align: center;
  font-size: 30px;
  color: #fff;
}
.ms-login {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 300px;
  margin: -100px 0 0 -190px;
  padding: 40px;
  border-radius: 5px;
  background: #fff;
}
.login-wrap.active {
  .ms-title {
    margin-top: -240px;
  }
  .ms-login {
    margin-top: -180px;
  }
}
.login-btn {
  text-align: center;
}
.login-btn button {
  width: 98px;
}
.demo-ruleForm .yzm {
  padding: 12px 8px;
  min-width: 88px;
}
</style>
