<template>
    <el-dialog title="上传" :visible.sync="uploadVisible" center>
        <el-upload
            class="upload-dialog upload-demo"
            ref="upload"
            name="files"
            :limit="1"
            :headers="header"
            :action="action"
            :on-error="handleError"
            :on-success="handleSuccess"
            :on-progress="handleProgress"
            :file-list="fileList"
            :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        </el-upload>
        <div slot="footer" class="dialog-footer">
            <el-button @click="uploadVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitUpload">上传</el-button>
        </div>
    </el-dialog>
</template>

<script>
    let token =  localStorage.getItem('adminToken');
    export default {
        name: "",
        data: function() {
            return {
                uploadVisible: false,
                fileList: [],
                loading: false,
                header: {token: token},
            }
        },
        props: {
            dis: {},
            action:{
                type:String,
                default:'/aquaman/trade/excel/readExcel',
            }
        },
        methods: {
            openUpload(){
                this.uploadVisible = true
            },
            submitUpload() {
                this.$refs.upload.submit();
            },
            handleProgress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '正在上传...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            handleError(){
                this.loading.close();
                this.$message({
                    message: "上传失败,请重试",
                    type: 'error'
                })
            },
            handleSuccess(res, file,fileList) {
                this.loading.close();
                if(res.code==401){
                    this.$message.error({
                        message: res.msg
                    });
                    window.location.href = "/#/login";
                    localStorage.setItem('token','');
                    return;
                }
                fileList=[];
                this.fileList=[];
                this.uploadVisible = false;
                if(res.code==0){
                    let order = res.comOrder;
                    if(order.productDetail.length>0){
                        for(let j=0;j<order.productDetail.length;j++){
                            let cjUnitId = order.productDetail[j].cjUnitId?order.productDetail[j].cjUnitId:'';
                            if(!cjUnitId)continue
                            let cj = cjUnit.filter(item=>{
                                let label = item.label.split("-")[1];
                                return label ==cjUnitId
                            });
                            try {
                                order.productDetail[j].cjUnitId = cj?cj[0].value:''
                            } catch(err) {}
                        }
                    }
                    this.$emit("uploadExcel",order);
                }else{
                    this.$message({
                        message: res.msg,
                        type: 'error'
                    })
                }
            },
        },

    }
</script>
<style>
    .upload-dialog .el-upload--text{width: auto;height: auto;border: none}
</style>
