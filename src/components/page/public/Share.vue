<template>
    <section id="app">
        <template v-if="type==1||type==2">
            <h3 class="title" v-if="type!=2">{{detail.article.title}}</h3>
            <section class="content">
                <div class="header">
                    <div class="head">
                        <div class="left-item">
                            <img class="" :src="detail.user.headIco" alt="">
                            <ul>
                                <li class="nickname">{{detail.user.nickName}}</li>
                                <li class="time">{{detail.article.updateTime}}</li>
                            </ul>
                        </div>
                        <a class="foucs_btn">+ 关注</a>
                    </div>
                </div>
                <div class="cont" v-html="detail.article.content">
                </div>
            </section>
            <section class="comment">
                <h3>评论({{detail.commentList.length}})</h3>
                <ul>
                    <template v-if="detail.commentList.length>0">
                        <li class="comment-item" v-for="(item,index) in detail.commentList">

                            <img :src="item.userHeadIco" alt="">
                            <div>
                                <div class="comment-userInfo">
                                    <span class="name">{{item.userNickName}}</span>
                                    <span class="cont">{{item.content}}</span>
                                </div>
                                <span class="time">{{item.createTime}}</span>
                            </div>
                        </li>
                    </template>
                    <template v-else>
                        <li class="comment-empty">暂无评论~</li>
                    </template>

                </ul>
            </section>
        </template>
        <template v-if="type==3">
            <h3 class="title">{{detail.questionTitle}}</h3>
            <section class="content">
                <div class="header">
                    <div class="head">
                        <div class="left-item">
                            <img class="" :src="detail.user.headIco" alt="">
                            <ul>
                                <li class="nickname">{{detail.user.nickName}}</li>
                                <li class="time">{{detail.createTime}}</li>
                            </ul>
                        </div>
                        <a class="foucs_btn">+ 关注</a>
                    </div>
                </div>
                <div class="cont" v-html="detail.content">
                </div>
            </section>
            <section class="comment">
                <h3>评论({{detail.commentList.length}})</h3>
                <ul>
                    <template v-if="detail.commentList.length>0">
                        <li class="comment-item" v-for="(item,index) in detail.commentList">

                            <img :src="item.userHeadIco" alt="">
                            <div>
                                <div class="comment-userInfo">
                                    <span class="name">{{item.userNickName}}</span>
                                    <span class="cont">{{item.content}}</span>
                                </div>
                                <span class="time">{{item.updateTime}}</span>
                            </div>
                        </li>
                    </template>
                    <template v-else>
                        <li class="comment-empty">暂无评论~</li>
                    </template>

                </ul>
            </section>
        </template>
        <template v-if="type==4">
            <h3 class="title">{{detail.title}}</h3>
            <section class="content">
                <div class="header">
                    <div class="head">
                        <div class="left-item">
                            <img class="" :src="detail.user.headIco" alt="">
                            <ul>
                                <li class="nickname">{{detail.user.nickName}}</li>
                                <li class="time">{{detail.updateTime}}</li>
                            </ul>
                        </div>
                        <a class="foucs_btn">+ 关注</a>
                    </div>
                </div>
                <video controls>
                    <source :src="detail.content" type="video/mp4">
                    <source :src="detail.content" type="video/ogg">
                    您的浏览器不支持 video 标签。
                </video>
            </section>
        </template>
    </section>
</template>

<script>
    export default {
        data: function(){
            return{
                type:1,  //1:文章，2:动态，3:答案，4:视频
                detail:{}
            }
        },
        mounted(){
            this.getDetail();
        },
        methods:{
            getDetail(){
                // let url = '/appdata/app/share/article/detail'  //文章动态详情接口地址，articleId
                // let url1 = '/appdata/app/share/answer/detail'  //答案详情接口地址，answerId
                // let url2 = '/appdata/app/share/video/detail'  //视频详情接口地址，articleId
                let self = this;
                if(self.type == 1 || self.type == 2){
                    self.$axios.get('/appdata/app/share/article/detail',{params:{articleId:1}}).then((res) => {
                        if (res.status == 1) {
                            self.detail = res.data;
                        }
                    });
                }else if(self.type == 3 ){
                    self.$axios.get('/appdata/app/share/answer/detail',{params:{answerId:3}}).then((res) => {
                        if (res.status == 1) {
                            self.detail = res.data;
                        }
                    });
                }else{
                    self.$axios.get('/appdata/app/share/video/detail',{params:{articleId:33}}).then((res) => {
                        if (res.status == 1) {
                            self.detail = res.data;
                        }
                    });
                }
            },
        }
    }
</script>
<style scoped>
    #app{background: #f3f3f3}
    *{padding: 0;margin: 0;}
    .title{font-size: 16px;padding: 10px;margin-bottom: 10px;font-weight: normal;background: #fff!important;line-height: 24px;}
    .content{background: #fff!important;display: block!important;}
    .content video{margin: 10px 0;width: 100%;}
    .header{padding: 10px;background: transparent!important;}

    .header img{width: 40px;height: 40px;display: inline-block;margin-right: 9px;border-radius: 40px}
    .header ul{height: 40px;display: flex;flex-wrap: wrap;flex-direction:column;justify-content: space-between}
    .header .nickname,.header .time{display: block}
    .header .nickname{font-size: 14px;color: #555555;overflow: hidden;text-overflow: ellipsis;white-space: nowrap}
    .header .time{font-size: 12px;color: #C0C0C0;}
    .head{display: flex;justify-content: space-between;flex-wrap: nowrap}
    .head .left-item{display: flex;justify-content: flex-start;flex: 1}
    .head .foucs_btn{flex-shrink:0;width: 50px;height: 20px;line-height: 20px;margin-top: 10px;background: #FFBB3C;color: #fff;font-size: 12px;text-align: center;border-radius: 3px}
    .content .cont{padding: 10px;}
    .comment{background: #fff;}
    .comment h3{font-size: 12px;font-weight: normal;text-indent: 10px;line-height: 40px;border-top: 1px solid #f0f0f0;border-bottom: 1px solid #f0f0f0}
    .comment ul{padding: 10px;}
    .comment ul .comment-empty{font-size: 12px;color: #999;}
    .comment ul .comment-item{display: flex;justify-content: space-between;margin-bottom: 10px}
    .comment ul .comment-item>div{display: flex;justify-content: space-between;flex: 1;border-bottom: 1px solid #f0f0f0;padding-bottom: 10px}
    .comment ul .comment-item img{width: 25px;height: 25px;border-radius: 25px;margin-right: 10px}
    .comment ul .comment-item .name{font-size: 12px;color: #333333}
    .comment ul .comment-item .cont{font-size: 12px;color: #999999}
    .comment ul .comment-item .time{font-size: 12px;color: #555555}
    .comment .comment-userInfo{display: flex;flex-direction: column;justify-content: space-between;height: 40px}





</style>
