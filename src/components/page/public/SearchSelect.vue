<template>
    <el-select
        v-model="svalue"
        :multiple="multiple"
        :value-key="valueKey"
        :clearable="clearable"
        :allow-create="allowCreate"
        :id="id"
        filterable
        remote
        reserve-keyword
        default-first-option
        :disabled="disabled"
        :placeholder="placeholder"
        :remote-method="debounce"
        :loading="loading"
        :popper-append-to-body="appendBody"
        @change="handleChange"
        @blur="handleBlur"
    >
        <el-option
            v-for="(item,index) in option"
            :key="index"
            :label="item[params.label]"
            :value="params.value?item[params.value]:item">
            {{params.itemName?item[params.itemName]:''}}
        </el-option>
    </el-select>
    <!--:key="item[params.label]+index"-->
</template>

<script>
    export default {
        name: "searchselect",
        data() {
            return {
                svalue:'',
                loading:false,
                option:[],//子组件的option
                timer:'',  //节流定时器
                count:0,
                unwatch:''
            }
        },
        watch:{
            //判断下拉框的值是否有改变
            // svalue(val, oldVal) {
            //     // console.log('new: %s, old: %s', val, oldVal);
            //     if(val!=oldVal){
            //         this.$emit('input', this.svalue);
            //         // this.handleChange();
            //     }
            // },
            value(val, oldVal) {
                // console.log('new: %s, old: %s', val, oldVal);
                if(val!=oldVal){
                    this.handleDefault(val)
                }
            },
            // options:{//深度监听，可监听到对象、数组的变化
            //     handler(val, oldVal){
            //         this.option = this.options;
            //     },
            //     deep:true
            // },
        },
        mounted() {
            //初始话下拉框的值
            let value = this.value;
            // this.$set(this,'svalue',value)
            this.unwatch = this.$watch('options', function (val, oldVal) {
                this.option = this.options;
            },{deep:true})
            let len = this.options.length;
            for(let i=0;i<len&&i<50;i++){
                this.$set(this.option,i,this.options[i])
                // this.option.push(this.options[i]);
            }
            if(value!=''){
                this.handleDefault(value)
            }

        },
        methods:{
            //筛选方法
            debounce (search) {
                let self = this;
                self.loading = true;
                // clearTimeout(self.timer);
                if(self.getData){
                    self.getData(search);
                    self.loading = false;
                    return
                }
                self.option = [];
                for(let i = 0,len=self.options.length; i < len; i++) {
                    // try {
                    //     if(item.hyId == self.value||item.abId == self.value||item.spId == self.value||item.lkId == self.value||item.supplierCode == value||item.codeValue == self.value||item.id == self.value){
                    //     }
                    // }catch (e) {
                    //     console.log(e);
                    // }
                    try {
                        if(self.options[i].label.toLowerCase().indexOf(search.toLowerCase())>=0)
                            self.option.push(self.options[i]);
                    }catch (e) {
                        console.log(e)
                    }
                }
                self.loading = false;
                // self.timer = setTimeout(function() {
                //     self.option = [];
                //     for(let i = 0,len=options.length; i < len; i++) {
                //         if(self.option.length>50)break;
                //         if(self.searchMethod(options[i],search))self.option.push(options[i]);
                //     }
                //     self.loading = false;
                // }, 10);
            },
            //处理下拉框默认值问题
            handleDefault(value){

                if(!value){
                    this.option=[];
                    for(let i=0;i<this.options.length&&i<50;i++){
                        this.option.push(this.options[i]);
                    }
                    this.svalue = value;
                    return
                }
                let obj = this.options.find((item)=>{
                    if(item.hyId!=undefined){
                        return item.hyId == value;
                    }else if(item.abId!=undefined){
                        return item.abId == value;
                    }else if(item.spId!=undefined){
                        return item.spId == value;
                    }else if(item.lkId!=undefined){
                        return item.lkId == value;
                    }else if(item.supplierCode!=undefined){
                        return item.supplierCode == value;
                    }else if(item.codeValue!=undefined){
                        value = ''+value;
                        return item.codeValue == value;
                    }else if(item.value!=undefined){
                        value = ''+value;
                        return item.value == value;
                    }else if(item.id!=undefined){
                        return item.id == value;
                    }
                });
                if(obj!=undefined){
                    this.option=[]
                    // this.$set(this.option,0,obj)
                    let isHad = false
                    // this.option.push(obj);
                    for(let i=0;i<this.options.length&&i<50;i++){
                        if(obj == this.options[i]){
                            isHad = true
                        }
                        let _i = this.option.length
                        this.$set(this.option,_i,this.options[i])
                        // this.option.push(this.options[i]);
                    }
                    if(!isHad){
                        this.$set(this.option,this.option.length,obj)
                    }
                }else{
                    this.option = [{

                    }]
                }
                this.$set(this,'svalue',value)
            },
            //处理变化响应
            handleChange(){
                if(this.svalue==''){ //如果清空选项不调用父组件的响应方法
                    return
                }
                this.$emit('input', this.svalue);
                this.change(this.svalue)
            },
            handleBlur(){
                // if(this.svalue==''||this.svalue==null||this.svalue==undefined)return
                // let val = this.svalue;
                // this.svalue = '';
                // this.svalue = val;
                // val=null
            }
        },
        props: {
            value:{

            },
            searchMethod:{
                type:Function,
                default:function () {
                    return '';
                }
            },
            getData:{
                type:Function,
            },
            id:{
                type:String,
                default:'',
            },
            valueKey:{
                type:String,
                default:'',
            },
            disabled:{
                type:Boolean,
                default:false,
            },
            clearable:{
                type:Boolean,
                default:false,
            },
            allowCreate:{
                type:Boolean,
                default:false,
            },
            appendBody:{
                type:Boolean,
                default:false,
            },
            params:{
                type:Object,
                default:{
                    key:'key',
                    label:'label',
                    value:'value',
                    itemName:'',
                }
            },
            placeholder:{
                type:String,
                default:'请输入关键词',
            },
            multiple:{
                type:Boolean,
                default:false,
            },
            change:{
                type:Function,
                default:function () {
                    return '';
                }
            },
            //父组件的options
            options:{
                type:Array,
                default:[],
            },
        },
        beforeDestroy:function(){
            this.unwatch();
            // this.unwatchOptions();
            console.log('销毁')
        },
    }
</script>

<style scoped lang="less">

</style>
