<template>
    <div>
        <el-table stripe :data="tableData" :row-key="rowKey" border @sort-change="sortChange" style="width: 100%" ref="multipleTable" @selection-change="selectionChange"
                  v-loading="loading"
                  :row-class-name="tableRowClassName"
                  element-loading-text="拼命加载中"
                  element-loading-spinner="el-icon-loading"
                  element-loading-background="rgba(0, 0, 0, 0.3)">
            <!--<el-table-column type="index" align="center" fixed label="序号"></el-table-column>-->
            <!-- prop: 字段名name, label: 展示的名称, fixed: 是否需要固定(left, right), minWidth: 设置列的最小宽度(不传默认值), oper: 是否有操作列
                 oper.name: 操作列字段名称, oper.clickFun: 操作列点击事件, formatData: 格式化内容 -->
            <template v-for="(th, key) in tableHeader">
                <el-table-column v-if="th.index" :label="th.label" type="index" :index="indexMethod" width="50" align="center" fixed>

                </el-table-column>
                <el-table-column v-else-if="th.type" type="selection" width="50"></el-table-column>

                <el-table-column v-else
                                 :key="`col_${key}`"
                                 :prop="dropTableHeader[key].prop?dropTableHeader[key].prop:''"
                                 :label="th.label"
                                 :fixed="th.fixed"
                                 :width="th.width"
                                 show-overflow-tooltip
                                 :sortable="th.sortable"
                                 :align="th.align?th.align:'center'"
                                 :min-width="th.minWidth">
                    <!-- 加入template主要是有操作一栏， 操作一栏的内容是相同的， 数据不是动态获取的，不过我这里操作一栏的名字定死了（oper表示是操作这一列，否则就不是） -->
                    <template slot-scope="scope">
                        <template v-if="dropTableHeader[key].oper">
                            <template v-for="(o, key) in dropTableHeader[key].oper">
                                <template v-if="o.statusKey">
                                    <el-tooltip :disabled="o.content==''||o.content==null||o.content==undefined" class="item" effect="dark" :content="o.content" placement="top-start">
                                        <a class="h-btn" v-if="(o.statusVal).indexOf(scope.row[o.statusKey])>=0" @click="o.clickFun(scope.row)"><i :class="['el-icon-'+o.class]"></i></a>
                                    </el-tooltip>

                                </template>
                                <template v-else>
                                    <el-tooltip :disabled="o.content==''||o.content==null||o.content==undefined" class="item" effect="dark" :content="o.content" placement="top-start">
                                        <a class="h-btn" @click="o.clickFun(scope.row)"><i :class="['el-icon-'+o.class]"></i></a>
                                    </el-tooltip>
                                </template>

                            </template>

                        </template>
                        <template v-else-if="dropTableHeader[key].download">
                            <a style="cursor: pointer" v-if="scope.row[dropTableHeader[key].prop]" @click="downloadFile(scope.row[dropTableHeader[key].prop])">点击下载</a>
                            <!--<a v-if="scope.row[dropTableHeader[key].prop]" :href="scope.row[dropTableHeader[key].prop]" :download="scope.row[dropTableHeader[key].prop]" target="_blank">点击下载</a>-->
                            <a v-else>暂无</a>
                        </template>
                        <template v-else-if="dropTableHeader[key].custom">
                            <span class="property-cont" @dblclick="dropTableHeader[key].custom(scope.row)">{{ scope.row[dropTableHeader[key].prop] }}</span>

                        </template>
                        <template v-else-if="dropTableHeader[key].click">
                            <span style="display: block;cursor: pointer" @click="dropTableHeader[key].click(scope.row)">{{ scope.row[dropTableHeader[key].prop] }}</span>
                        </template>
                        <template v-else-if="dropTableHeader[key].message" >
                            <router-link :to="{path:'/home/<USER>/detail',query:{id:scope.row.id}}" class="message-content" >{{ scope.row[dropTableHeader[key].prop] }}
                            </router-link>
                        </template>
                        <template v-else-if="dropTableHeader[key].url">
                            <a :href="dropTableHeader[key].url+scope.row[dropTableHeader[key].prop]+'.pdf'" target="_blank" v-if="scope.row[dropTableHeader[key].prop]">{{ scope.row[dropTableHeader[key].prop] }}</a>
                            <span v-else>{{ scope.row[dropTableHeader[key].prop] }}</span>
                        </template>
                        <template v-else>
                            <span v-if="!dropTableHeader[key].formatData">{{ scope.row[dropTableHeader[key].prop] }}</span>
                            <span v-else>{{ scope.row[dropTableHeader[key].prop] | formatters(dropTableHeader[key].formatData) }}</span>
                        </template>
                    </template>
                </el-table-column>
            </template>

        </el-table>
        <div class="text-center mt_2">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="params.page"
                :page-sizes="[50, 100, 300, 500]"
                :page-size="params.limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
    import Sortable from 'sortablejs'
    export default {
        props: {
            tableData: {
                type: Array,
                default: function () {
                    return []
                }
            },
            dropTableHeader: {
                type: Array,
                default: function () {
                    return []
                }
            },
            params: {
                type: Object
            },
            total: {
                type: Number
            },
            sortChange: {
                type: Function
            },
            selectionChange: {
                type: Function,
                default: function () {
                    return []
                }
            },
            handleSizeChange: {
                type: Function
            },
            handleCurrentChange: {
                type: Function
            },
            rowKey: String,
            tableHeader: {
                type: Array,
                default: function () {
                    return []
                }
            },
            loading: {
                type: Boolean,
                default: false
            },
            tableRowClassName: {
                type: Function,
                default: function () {

                }
            },
            selection: {
                type: Boolean,
                default: false
            },
        },
        mounted(){
            this.columnDrop()
        },
        methods:{
            indexMethod(index){  //序号自定义
                let _index = (this.params.page-1)*this.params.limit+index+1
                return _index;
            },
            columnDrop() {
                const wrapperTr = document.querySelector('.el-table__header-wrapper tr');

                this.sortable = Sortable.create(wrapperTr, {
                    animation: 180,
                    delay: 0,
                    onEnd: evt => {
                        const oldItem = this.dropTableHeader[evt.oldIndex]
                        this.dropTableHeader.splice(evt.oldIndex, 1)
                        this.dropTableHeader.splice(evt.newIndex, 0, oldItem)
                    }
                })
            },
            downloadFile(url){
                this.$util.downloadFile(url);
            }
        }
    }
</script>

<style>
    .el-table .warning-row {
        background: #f0f9eb;
    }
    .el-table .error-row {
        background: #f56c6c;
    }

</style>
