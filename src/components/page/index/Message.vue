<template>
    <div>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="danger" @click="handleDelete('')">删除</el-button>
                <el-button type="success" @click="handleRead('')">标记已读</el-button>
                <el-button type="primary" @click="handleAllRead">全部已读</el-button>


            </el-row>
            <el-tabs type="card" v-model="params.type" @tab-click="getList">
                <el-tab-pane label="分类1" name="0"></el-tab-pane>
                <el-tab-pane label="分类2" name="1"></el-tab-pane>
            </el-tabs>
            <v-table
                :selection="selection"
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :selectionChange="handleSelectionChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>
    </div>
</template>

<script>
    import vTable from './../public/Table';
    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    type:'',
                    sidx:'',
                    order:'',
                },
                multipleSelection:'',
                total:0,
                totalPage:1,
                tableData: [],
                rowKey:'id',
                selection:true,
                tableHeader: [
                    { type:true},
                    { prop: 'messageTitle', label: '标题内容',sortable:'custom',click:this.goDetail},
                    { prop: 'messageTitle', label: '提交时间',sortable:'custom'},
                    { prop: 'type', label: '类型',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { type:true},
                    { prop: 'messageTitle', label: '标题内容',sortable:'custom'},
                    { prop: 'messageTitle', label: '提交时间',sortable:'custom'},
                    { prop: 'type', label: '类型',sortable:'custom'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/base/messagemy/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                });
            },
            goDetail(row){
                this.$router.push({path: '/home/<USER>/message/detail',query:{id:row.id}})

            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleAllRead(){},
            handleRead(){

            },
            handleDelete(row){
                let self = this,ids=[];
                if(!row.id){
                    if(self.multipleSelection==undefined||self.multipleSelection==''){
                        self.$message({type: 'error', message: '请选择需要删除的消息'});
                        return false;
                    }
                    self.multipleSelection.map((item)=>{
                        ids.push(item.id)
                    });
                    ids = ids.join(",");
                }else{
                    ids = row.id;
                }
                self.$confirm('是否确认删除消息?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/base/messagemy/delete',[ids]).then((res) => {
                        if(res.status==1){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vTable
        }
    }
</script>

