<template>
    <el-row>
        首页待定
        <!--<el-col :span="24">-->
            <!--<el-row class="white-bg">-->
                <!--<el-col :span="24" class="top-block">-->
                    <!--<div class="top-block-item num1">-->
                        <!--<p class="text-center">昨日注册用户数</p>-->
                        <!--<div class="text-center">{{yesterdayUserCount}}</div>-->
                    <!--</div>-->
                    <!--<div class="top-block-item num2">-->
                        <!--<p class="text-center">当前在线人数</p>-->
                        <!--<div class="text-center">{{nowOoLineCount}}</div>-->
                    <!--</div>-->
                    <!--<div class="top-block-item num3">-->
                        <!--<p class="text-center">用户总数</p>-->
                        <!--<div class="text-center">{{allUserCount}}</div>-->
                    <!--</div>-->
                <!--</el-col>-->
            <!--</el-row>-->
        <!--</el-col>-->
        <!--<el-col :span="18">-->
            <!--<el-row class="white-bg">-->
                <!--<el-col :span="24">-->
                    <!--<div class="bottom-block cl">-->
                        <!--<p class="title cl">昨日新增用户详情 <router-link class="fr" style="font-size: 12px;color: #666" to="/user/data">查看更多>></router-link></p>-->
                        <!--<div class="charts fl" style="height: 300px;width: 50%;">-->
                            <!--<div id="myChart1" style="height: 100%;width: 100%;"></div>-->
                        <!--</div>-->
                        <!--<div class="charts fl" style="height: 300px;width: 50%;">-->
                            <!--<div id="myChart2" style="height: 100%;width: 100%;"></div>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</el-col>-->
            <!--</el-row>-->
            <!--<el-row class="white-bg">-->
                <!--<el-col :span="24">-->
                    <!--<div class="bottom-block cl">-->
                        <!--<p class="title cl">昨日用户活跃详情 <router-link class="fr" style="font-size: 12px;color: #666" to="/user/data">查看更多>></router-link></p>-->
                        <!--<div class="cl">-->
                            <!--<p style="width: 50%" class="fl text-center">昨日用户在线情况(时间维度)</p>-->
                            <!--<p style="width: 50%" class="fl text-center">昨日文章阅读量排行榜</p>-->
                        <!--</div>-->
                        <!--<div class="charts fl" style="height: 300px;width: 50%;">-->
                            <!--<div id="myChart3" style="height: 100%;width: 100%;"></div>-->
                        <!--</div>-->
                        <!--<div class="charts fl" style="width: 50%;">-->
                            <!--<ul class="article-list">-->
                                <!--<template v-if="article.length>0">-->
                                    <!--<li v-for="(item,index) in article"><span :class="{'red':index<=2}">{{index+1}}</span>{{item.title}}</li>-->
                                <!--</template>-->
                                <!--<template v-else>-->
                                    <!--<li class="text-center" style="color: #999">暂无文章热度~</li>-->
                                <!--</template>-->
                            <!--</ul>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</el-col>-->
            <!--</el-row>-->
        <!--</el-col>-->
        <!--<el-col :span="6" class="right-box">-->
            <!--<el-row>-->
                <!--<el-col :span="24">-->
                    <!--<div class="right-top">-->
                        <!--<p class="title">用户最新消息<router-link class="fr" style="font-size: 12px;color: #666" to="/message/feedback">查看更多>></router-link></p>-->
                        <!--<ul>-->
                            <!--&lt;!&ndash;<li class="cl" v-for="(item,index) in rightData.productOfMonth">&ndash;&gt;-->
                                <!--&lt;!&ndash;<span class="fl" :class="{'color1':index==0,'color2':index==1,'color3':index==2}">{{index+1}}</span><img class="fl" :src="item.imagePath" alt="">&ndash;&gt;-->
                                <!--&lt;!&ndash;<div class="fl">&ndash;&gt;-->
                                    <!--&lt;!&ndash;<p>{{item.productName}}</p>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<span>{{parseInt(item.productNum)}}吨</span>&ndash;&gt;-->
                                <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;</li>&ndash;&gt;-->

                        <!--</ul>-->
                    <!--</div>-->
                <!--</el-col>-->
            <!--</el-row>-->
            <!--<el-row>-->
                <!--<el-col :span="24">-->
                    <!--<div class="right-bottom">-->
                        <!--<p class="title">最近登录情况<router-link class="fr" style="font-size: 12px;color: #666" to="/setting/login-log">查看更多>></router-link></p>-->
                        <!--<ul>-->
                            <!--<li v-for="(item,index) in loginLog">-->
                                <!--<span>IP:{{item.loginIp}}</span>-->
                                <!--<span>登录时间:{{item.loginTime}}</span>-->
                            <!--</li>-->
                        <!--</ul>-->
                    <!--</div>-->
                <!--</el-col>-->
            <!--</el-row>-->
        <!--</el-col>-->

    </el-row>
</template>
<script>
    // import echarts from 'echarts'
    export default {
        data: function(){
            return {
                yesterdayUserCount:'',
                nowOoLineCount:'',
                allUserCount:'',
                loginLog:[],
                article:[],
                option1:{
                    // title:{
                    //     text:'表格',
                    //     textStyle:{
                    //         align:'right',
                    //         fontSize:'14px'
                    //     }
                    // },
                    tooltip : {
                        trigger: 'item',
                        formatter: "{a} <br/>{b} : {c} ({d}%)"
                    },
                    legend: {
                        // orient: 'vertical',
                        // top: 'middle',
                        bottom: 10,
                        left: 'center',
                        data: ['西凉', '益州','兖州','荆州']
                    },
                    series : [
                        {
                            name: '类目',
                            type: 'pie',
                            radius : '65%',
                            center: ['50%', '50%'],
                            selectedMode: 'single',
                            data:[
                                {value:535, name: '荆州'},
                                {value:510, name: '兖州'},
                                {value:634, name: '益州'},
                                {value:735, name: '西凉'}
                            ],
                            itemStyle: {
                                emphasis: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ],
                    color:['#0181FE','#FE93C1']
                },
                option2:{
                    tooltip: {
                        trigger: 'axis'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            name:'人数',
                            type:'line',
                            stack: '总量',
                            data: []
                        }
                    ],
                    color:["#53DDD3"]
                },
                option3:{
                    tooltip: {
                        trigger: 'axis'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            name:'人数',
                            type:'line',
                            stack: '总量',
                            data: []
                        }
                    ],
                    color:["#53DDD3"]
                },
                // option2:{
                //     tooltip: {
                //         trigger: 'axis'
                //     },
                //     grid: {
                //         left: '3%',
                //         right: '4%',
                //         bottom: '3%',
                //         containLabel: true
                //     },
                //     xAxis: {
                //         type: 'category',
                //         boundaryGap: false,
                //         data: ['周一','周二','周三','周四','周五','周六','周日']
                //     },
                //     yAxis: {
                //         type: 'value'
                //     },
                //     series: [
                //         {
                //             name:'订单数',
                //             type:'line',
                //             stack: '总量',
                //             data:[120, 132, 101, 134, 90, 230, 210]
                //         }
                //     ],
                //     color:["#53DDD3"]
                // },

            }
        },
        created(){

        },
        mounted(){
            // this.getUserCount();
            // this.getData();
        },
        methods:{
            toast (str) {
                let v = this
                v.toastText = str
                v.toastShow = true
                setTimeout(function () {
                    v.toastShow = false
                }, 1500)
            },
            getUserCount(){
                let self = this;
                self.$axios.get('/admin/index/yesterdayUserCount').then((res) => {
                    if(res.status==1){
                        self.yesterdayUserCount = res.data;
                    }
                });
                self.$axios.get('/admin/index/nowOoLineCount').then((res) => {
                    if(res.status==1){
                        self.nowOoLineCount = res.data;
                    }
                });
                self.$axios.get('/admin/index/allUserCount').then((res) => {
                    if(res.status==1){
                        self.allUserCount = res.data;
                    }
                });
            },
            getData(){
                let self = this,data1,data2,data3=[];
                self.$axios.get('/admin/index/queryAndroIos').then((res) => {
                    if(res.status==1){
                        if(res.data.length==1){
                            if(res.data[0].type==1){
                                self.option1.series[0].data = [{name:'安卓',value:res.data[0].countNum},{name:'IOS',value:0}];
                            }else{
                                self.option1.series[0].data = [{name:'安卓',value:0},{name:'IOS',value:res.data[0].countNum}];
                            }
                        }else if(res.data.length==2){
                            self.option1.series[0].data=[];
                            for(let i=0;i<res.data.length;i++){
                                if(res.data[i].type==1){
                                    self.option1.series[0].data.push({name:'安卓',value:res.data[0].countNum});
                                }else if(res.data[i].type==2){
                                    self.option1.series[0].data.push({name:'IOS',value:res.data[0].countNum});
                                }
                            }
                        }else{
                            self.option1.series[0].data = [{name:'安卓',value:0},{name:'IOS',value:0}];
                        }
                        let mainChart1 = self.$echarts.init(myChart1);
                        mainChart1.setOption(self.option1);
                    }
                });
                self.$axios.get('/admin/index/queryYersterDayUserCount').then((res) => {
                    if(res.status==1) {
                        // self.option2.xAxis.data = res.data.date
                        // self.option2.series[0].data = res.data.userCount;
                        // let mainChart2 = self.$echarts.init(myChart2);
                        // mainChart2.setOption(self.option2);
                        let date = ['00','01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23'];
                        let count = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
                        for(let i=0;i<date.length-1;i++){
                            if(res.data!=null){
                                for(let j=0;j<res.data.date.length-1;j++){
                                    if(date[i]==res.data.date[j]){
                                        count[i]=res.data.userCount[j]
                                    }
                                }
                            }
                        }
                        self.option2.series[0].data = count;
                        self.option2.xAxis.data=date;
                        let mainChart2= self.$echarts.init(myChart2);
                        mainChart2.setOption(self.option2);
                    }
                });
                self.$axios.get('/admin/index/queryUserYesAlive').then((res) => {
                    if(res.status==1){
                        let date = ['00','01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23'];
                        let count = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
                        for(let i=0;i<date.length-1;i++){
                            for(let j=0;j<res.data.date.length-1;j++){
                                if(date[i]==res.data.date[j]){
                                    count[i]=res.data.userCount[j]
                                }
                            }
                        }
                        self.option3.series[0].data = count;
                        self.option3.xAxis.data=date;
                        let mainChart3= self.$echarts.init(myChart3);
                        mainChart3.setOption(self.option3);
                    }
                });
                self.$axios.get('/admin/index/queryLoginLogsTop5').then((res) => {
                    if(res.status==1){
                        self.loginLog = res.data
                    }
                });
                self.$axios.get('/admin/index/queryArticalTop7').then((res) => {
                    if(res.status==1){
                        self.article = res.data;
                    }
                });

            },
            getDay(day){
                let today = new Date();
                let targetday_milliseconds=today.getTime() + 1000*60*60*24*day;
                today.setTime(targetday_milliseconds); //注意，这行是关键代码
                let tYear = today.getFullYear();
                let tMonth = today.getMonth();
                let tDate = today.getDate();
                tMonth = this.doHandleMonth(tMonth + 1);
                tDate = this.doHandleMonth(tDate);
                return tMonth+"-"+tDate;
            },
            doHandleMonth(month){
                let m = month;
                if(month.toString().length == 1){
                    m = "0" + month;
                }
                return m;
            }

    }
    }
</script>

<style scoped lang="less" ref="stylesheet/less">
    .top-block{
        display: flex;justify-content: space-between;
        .top-block-item{
            width: 32%;line-height: 50px;padding: 10px 0;color: #fff;border-radius: 20px;
            p{font-weight: bold}
            div{font-size: 22px;font-weight: bold}
        }
        .num1{background: url("../../../assets/item1.png") no-repeat;background-size: 100% 100%;}
        .num2{background: url("../../../assets/item2.png") no-repeat;background-size: 100% 100%;}
        .num3{background: url("../../../assets/item3.png") no-repeat;background-size: 100% 100%;}
    }
    .mid-table{
        width: 100%;border-collapse:collapse;border-left: 1px solid #E3E3E3;border-bottom: 1px solid #E3E3E3;border-right: 1px solid #E3E3E3;
        tr th{background: #242f42;color: #fff;}
        tr th,tr td{text-align: center;font-size: 16px;font-weight: normal;border: none;line-height: 40px}
        tr td{padding: 10px 0;font-size: 18px;}
    }
    .bottom-block{border: 1px solid #E3E3E3;margin-top: 20px;}
    .title{font-size: 16px;font-weight: bold;text-align: left;line-height: 30px;margin-top: 10px;padding: 0 20px}
    .right-box{
        padding: 0 0 0 10px;box-sizing: border-box;
        .right-top{
            background: #fff;overflow: hidden;
            .title{margin-bottom: 20px}
            ul li{
                line-height: 20px;margin-bottom: 20px;padding-left: 10px;
                >span{line-height: 20px;margin: 10px 20px 10px 0;text-align: center;width: 20px;height: 20px;color: #fff;background: #2E363F;border-radius: 20px;display: inline-block;float: left;font-size: 12px;}
                div>span{font-size: 14px;color: #999}
            }
            img{width: 40px;height: 40px;float: left;margin-right: 16px}
        }
        .right-bottom {
            margin-top: 20px;background: #fff;overflow: hidden;padding-bottom: 20px;
            .title{margin-bottom: 10px}
            ul{
                span{display: block;line-height: 24px;text-indent: 20px;font-size: 14px}
            }
        }
    }
    .content{background: #F4F4FA}
    .color1{background: red!important;}
    .color2{background: #00D4C9!important;}
    .color3{background: #0181FE!important;}
    .article-list{
        margin-top: 20px;
        li{
            line-height: 30px;color: #333;
            span{display: inline-block;width: 20px;float: left;margin: 5px;height: 20px;background: #666;border-radius: 3px;text-align: center;line-height: 20px;color: #fff;font-size: 12px;}
            span.red{background: red}
        }
    }


</style>
