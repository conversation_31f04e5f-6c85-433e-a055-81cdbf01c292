<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="商品名称">
                        <el-input v-model="params.productName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="HSCODE">
                        <el-input v-model="params.hsCode" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="商品状态">
                        <el-select v-model="params.productStuats" placeholder="请选择商品状态">
                            <el-option
                                v-for="item in STATUS.productStuats"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

            </template>
        </v-search>
        <div class="p_1">
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    productName:'',
                    hsCode:'',
                    productStuats:'',
                },
                rowKey:'id',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'companyName', label: '公司名称',sortable:'custom',minWidth:'140px'},
                    { prop: 'companyCode', label: '公司编号',sortable:'custom',minWidth:'140px'},
                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',minWidth:'140px'},
                    { prop: 'productName', label: '商品名',sortable:'custom',minWidth:'200px'},
                    { prop: 'productEnName', label: '英文名称',sortable:'custom',minWidth:'200px'},
                    { prop: 'countryName', label: '原产国',sortable:'custom',minWidth:'140px'},
                    { prop: 'faUnit', label: '法定单位',sortable:'custom',minWidth:'140px',formatData: this.FILTER.unit},
                    { prop: 'productPrice', label: '单价＄',sortable:'custom',minWidth:'100px'},
                    { prop: 'productWeight', label: '重量KG',sortable:'custom',minWidth:'100px'},
                    { prop: 'productStuats', label: '状态',sortable:'custom',minWidth:'140px',formatData: this.FILTER.productStuats},
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'companyName', label: '公司名称',sortable:'custom',minWidth:'140px'},
                    { prop: 'companyCode', label: '公司编号',sortable:'custom',minWidth:'140px'},
                    { prop: 'hsCode', label: 'HSCODE',sortable:'custom',minWidth:'140px'},
                    { prop: 'productName', label: '商品名',sortable:'custom',minWidth:'200px'},
                    { prop: 'productEnName', label: '英文名称',sortable:'custom',minWidth:'200px'},
                    { prop: 'countryName', label: '原产国',sortable:'custom',minWidth:'140px'},
                    { prop: 'faUnit', label: '法定单位',sortable:'custom',minWidth:'140px',formatData: this.FILTER.unit},
                    { prop: 'productPrice', label: '单价＄',sortable:'custom',minWidth:'100px'},
                    { prop: 'productWeight', label: '重量KG',sortable:'custom',minWidth:'100px'},
                    { prop: 'productStuats', label: '状态',sortable:'custom',minWidth:'140px',formatData: this.FILTER.productStuats},
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/logistics/comlogisticsproduct/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

