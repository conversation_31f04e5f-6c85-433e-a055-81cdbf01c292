<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span>详情</span></p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-form-item align="right">
                    <el-button @click="back()">取消</el-button>
                    <el-button type="primary" @click="handleStatus">{{ruleForm.coStatus==2?'启用账号':'账号冻结'}}</el-button>
                    <el-button type="danger" @click="handleDelete" v-if="ruleForm.coStatus==1">账号删除</el-button>
                </el-form-item>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公司名称:" prop="exName">
                            {{ruleForm.exName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人:" prop="contactPeople" >
                            {{ruleForm.contactPeople}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公司编码:" prop="companyCode">
                            {{ruleForm.companyCode}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话:" prop="telephone">
                            {{ruleForm.telephone}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="管理账号:" prop="adminUser" >
                            {{ruleForm.adminUser}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="授权开始时间:" prop="authorizeStartTime">
                            {{ruleForm.authorizeStartTime}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="登录密码:" prop="adminPassword" >
                            {{ruleForm.adminPassword}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="授权结束时间:" prop="authorizeEndTime">
                            {{ruleForm.authorizeEndTime}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="统一社会信用代码:" prop="unifyCode" >
                            {{ruleForm.unifyCode}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所在国家:" prop="countryId" >
                            {{ruleForm.countryName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                    <el-form-item label="公司地址:" prop="companyAddress">
                        {{ruleForm.companyAddress}}
                    </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公司logo:" prop="companyLogo">
                            <el-upload
                                class="avatar-uploader"
                                action="/logManager/logistics/file/uploads"
                                name="files"
                                :headers="header"
                                :show-file-list="false"
                                :on-success="handleSuccess"
                                :on-error="handleError"
                                :on-progress="handleProgress"
                                :before-upload="beforeUpload">
                                <img v-if="ruleForm.companyLogo" :src="ruleForm.companyLogo" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
           <el-row v-if="log.length>0">
               <p class="title">异动记录</p>
               <el-table :data="log" border style="width: 100%">
                   <el-table-column align="center" label="序号" width="50" type="index"></el-table-column>
                   <el-table-column align="center" prop="type" label="异动类型" min-width="120"></el-table-column>
                   <el-table-column align="center" prop="remark" label="异动备注" min-width="300"></el-table-column>
                   <el-table-column align="center" prop="createUserName" label="操作人" min-width="100"></el-table-column>
                   <el-table-column align="center" prop="createTime" label="操作时间" min-width="120"></el-table-column>
               </el-table>
           </el-row>
        </div>
    </div>
</template>
<script>
    let token =  localStorage.getItem('adminToken');
    export default {
        data: function () {
            return {
                ruleForm: {
                    exId:'',
                    exName: '',
                    contactPeople: '',
                    companyCode: '',
                    telephone: '',
                    adminUser: '',
                    adminName: '',
                    adminPassword: '',
                    authorizeStartTime: '',
                    authorizeEndTime: '',
                    unifyCode: '',
                    companyAddress: '',
                    companyLogo: '',
                    countryId: '',
                    countryName: '',
                },
                rules: {
                    exName: [
                        { required: true, message: '请输入公司名称', trigger: 'blur' }
                    ],
                    adminUser: [
                        { required: true, message: '请输入管理账号', trigger: 'blur' }
                    ],
                    adminName: [
                        { required: true, message: '请输入联系人', trigger: 'blur' }
                    ],
                    adminPassword: [
                        { required: true, message: '请输入登录密码', trigger: 'blur' },
                        { pattern:/^.{6,}$/, message: '请输入正确的登录密码', trigger: 'blur' }
                    ],
                    telephone: [
                        { required: true, message: '请输入联系电话', trigger: 'blur' },
                        { pattern:/^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                    ],
                    companyCode: [
                        { required: true, message: '请输入公司编码', trigger: 'blur' }
                    ],
                    authorizeStartTime: [
                        { required: true, message: '请选择授权开始时间', trigger: 'change' }
                    ],
                    authorizeEndTime: [
                        { required: true, message: '请选择授权结束时间', trigger: 'change' }
                    ],
                    countryId: [
                        { required: true, message: '请选择所在国家', trigger: 'blur' }
                    ],
                },
                log:[],//异动记录
                loading:'',
                header: {token: token},
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < Date.now();
                    },
                    shortcuts: [{
                        text: '一年',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24*365);
                            picker.$emit('pick', date);
                        }
                    }, {
                        text: '三年',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24*365*3);
                            picker.$emit('pick', date);
                        }
                    }]
                },
            }
        },
        mounted(){
            let myDate = new Date();
            this.ruleForm.startTime = myDate.getFullYear()+'-'+(myDate.getMonth()+1)+'-'+myDate.getDate();
            if(this.$route.query.id){
                this.ruleForm.exId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let url = '/logistics/couriercompany/info/'+this.ruleForm.exId;
                this.$axios.get(url).then((res) => {
                    if(res.code==0){
                        this.ruleForm = res.courierCompany;
                        this.getLog();
                    }
                });
            },
            getLog(){
                this.$axios.get('/logistics/couriercompany/listByCompany/'+this.ruleForm.exId).then((res) => {
                    if(res.code==0){
                        this.log = res.list;
                    }
                });
            },
            checkCode(){
                let self = this;
                if(self.ruleForm.companyCode=='')return;
                let url = '/trade/baseCon/getCodeExist/'+self.ruleForm.companyCode;
                self.$axios.get(url).then((res) => {
                    if(!res){
                        self.ruleForm.companyCode='';
                    }
                });
            },
            handleProgress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '正在上传图片...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            handleError(){
                this.loading.close();
                this.$message({
                    message: "上传失败,请重试",
                    type: 'error'
                })
            },
            handleSuccess(res, file) {
                this.loading.close();
                if(res.code!=0){
                    this.$message({
                        message: res.msg,
                        type: 'error'
                    })
                    if(res.code==401){
                        localStorage.setItem('adminToken','');
                        window.location.href = '/superadmin/#/login'
                    }
                }else{
                    this.ruleForm.companyLogo = res.result;
                }
            },
            beforeUpload(file) {
                const isJPG = (file.type).indexOf("image/")!=-1
                const isLt = file.size / 1024 / 1024 < 10;

                if (!isJPG) {
                    this.$message.error('请上传正确的图片格式!');
                }
                if (!isLt) {
                    this.$message.error('上传图片大小不能超过10MB!');
                }
                return isJPG && isLt;
            },
            handleStatus(){
                let str = this.ruleForm.coStatus==2?'是否确认启用该快递公司':'是否确认冻结该快递公司'
                let reg = this.ruleForm.coStatus==2?'':/\S/
                this.$prompt('异动原因', '异动原因', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: reg,
                    inputErrorMessage: '请输入冻结备注'
                }).then(({ value }) => {
                    this.$axios.post('/logistics/couriercompany/updateCompanyStatus',{
                        exId: this.ruleForm.exId,
                        coStatus:this.ruleForm.coStatus==2?1:2,
                        remark:value
                    }).then((res) => {
                        if(res.code==0){
                            this.$message({
                                type: 'success',
                                message: '冻结成功!'
                            });
                            this.getDetail();
                        }
                    });
                }).catch(() => {

                });
            },
            handleDelete(){
                this.$confirm('是否确认删除该快递公司?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    this.$axios.post('/logistics/couriercompany/removeCompany/'+this.ruleForm.exId).then((res) => {
                        if(res.code==0){
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            this.back();
                        }
                    });
                }).catch(() => {

                });
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
