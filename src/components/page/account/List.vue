<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="公司名称">
                        <el-input v-model="params.exName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="账号状态">
                        <el-select v-model="params.coStatus" placeholder="请选择账号状态">
                            <el-option
                                v-for="item in STATUS.coStatus"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="所在国家">
                        <country @change="country=>this.params.countryId=country[0]" :countryId="params.countryId"></country>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>
    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';
    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    exName:'',
                    coStatus:'',
                    countryId:'',
                },
                today:'',
                rowKey:'exId',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'exName', label: '公司名称',sortable:'custom',minWidth:'140px'},
                    { prop: 'companyCode', label: '公司编码',sortable:'custom',minWidth:'140px'},
                    { prop: 'adminUser', label: '管理账号',sortable:'custom',minWidth:'140px'},
                    { prop: 'contactPeople', label: '联系人',sortable:'custom',minWidth:'140px'},
                    { prop: 'telephone', label: '联系电话',sortable:'custom',minWidth:'140px'},
                    { prop: 'authorizeStartTime', label: '授权日期',sortable:'custom',minWidth:'140px'},
                    { prop: 'authorizeEndTime', label: '授权到期',sortable:'custom',minWidth:'140px'},
                    { prop: 'timeDiffer', label: '剩余到期时间',sortable:'custom',minWidth:'160px'},
                    { prop: 'coStatus', label: '账号状态',sortable:'custom',minWidth:'140px',formatData: this.FILTER.coStatus},
                    { prop: 'oper', label: '操作', fixed: 'right',width:'140px',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit,content:'编辑'},
                            { class: 'zoom-in', clickFun: this.handleDetail,content:'详情'},
                            { class: 'lock',statusKey:'coStatus',statusVal:['1'], clickFun: this.handleStatus,content:'冻结'},
                            { class: 'unlock',statusKey:'coStatus',statusVal:['2'], clickFun: this.handleStatus,content:'解冻'},
                            { class: 'delete', clickFun: this.handleDelete,content:'删除'},
                        ]
                    },
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'exName', label: '公司名称',sortable:'custom'},
                    { prop: 'companyCode', label: '公司编码',sortable:'custom'},
                    { prop: 'adminUser', label: '管理账号',sortable:'custom'},
                    { prop: 'contactPeople', label: '联系人',sortable:'custom'},
                    { prop: 'telephone', label: '联系电话',sortable:'custom'},
                    { prop: 'authorizeStartTime', label: '授权日期',sortable:'custom'},
                    { prop: 'authorizeEndTime', label: '授权到期',sortable:'custom'},
                    { prop: 'timeDiffer', label: '剩余到期时间',sortable:'custom'},
                    { prop: 'coStatus', label: '账号状态',sortable:'custom',formatData: this.FILTER.coStatus},
                    { prop: 'oper', label: '操作', fixed: 'right',width:'140px',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit,content:'编辑'},
                            { class: 'zoom-in', clickFun: this.handleDetail,content:'详情'},
                            { class: 'lock',statusKey:'coStatus',statusVal:['1'], clickFun: this.handleStatus,content:'冻结'},
                            { class: 'unlock',statusKey:'coStatus',statusVal:['2'], clickFun: this.handleStatus,content:'解冻'},
                            { class: 'delete', clickFun: this.handleDelete,content:'删除'},
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
            this.today = new Date().getFullYear()+'/'+(new Date().getMonth()+1)+'/'+new Date().getDate();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/logistics/couriercompany/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                    self.tableData.map(item=>{
                        item.timeDiffer = this.$util.timeDiffer(item.authorizeEndTime,this.today)+'天'
                    })

                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/edit',query:{id:row.exId}})
            },
            handleDetail(row){
                this.$router.push({path: '/home/<USER>/detail',query:{id:row.exId}})
            },
            handleDelete(row){
                let self = this;
                self.$confirm('是否确认删除该快递公司?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    self.$axios.post('/logistics/couriercompany/removeCompany/'+row.exId).then((res) => {
                        if(res.code==0){
                            self.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            self.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            handleStatus(row){
                let str = row.coStatus==2?'是否确认启用该快递公司':'是否确认冻结该快递公司'
                let reg = row.coStatus==2?'':/\S/
                this.$prompt('异动原因', '异动原因', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: reg,
                    inputErrorMessage: '请输入备注'
                }).then(({ value }) => {
                    this.$axios.post('/logistics/couriercompany/updateCompanyStatus',{
                        exId: row.exId,
                        coStatus:row.coStatus==2?1:2,
                        remark:value
                    }).then((res) => {
                        if(res.code==0){
                            this.$message({
                                type: 'success',
                                message: '操作成功!'
                            });
                            this.getList();
                        }
                    });
                }).catch(() => {

                });
            },
            setCountry(country){
                this.params.countryId=country[0]
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

