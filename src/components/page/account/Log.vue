<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="公司名称">
                        <el-input v-model="params.companyName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="管理账号">
                        <el-input v-model="params.username" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>

            </template>
        </v-search>
        <div class="p_1">
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from './../public/Search';
    import vTable from './../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    username:'',
                    companyName:'',
                },
                rowKey:'id',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'companyName', label: '异动账号',sortable:'custom',minWidth:'140px'},
                    { prop: 'type', label: '异动类型',sortable:'custom',minWidth:'140px'},
                    { prop: 'remark', label: '异动原因',sortable:'custom',minWidth:'200px'},
                    { prop: 'createUserName', label: '操作人',sortable:'custom',minWidth:'140px'},
                    { prop: 'createTime', label: '操作时间',sortable:'custom',minWidth:'140px'},
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'companyName', label: '异动账号',sortable:'custom',minWidth:'140px'},
                    { prop: 'type', label: '异动类型',sortable:'custom',minWidth:'140px'},
                    { prop: 'remark', label: '异动原因',sortable:'custom',minWidth:'200px'},
                    { prop: 'createUserName', label: '操作人',sortable:'custom',minWidth:'140px'},
                    { prop: 'createTime', label: '操作时间',sortable:'custom',minWidth:'140px'},
                ],
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/logistics/userrecording/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);

                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

