<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.exId">编辑</span><span v-else>新建</span></p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公司名称:" prop="exName">
                            <el-input v-model="ruleForm.exName" disabled v-if="ruleForm.exId"></el-input>
                            <el-input v-model="ruleForm.exName" v-else @blur="checkName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人:" prop="contactPeople" >
                            <el-input v-model="ruleForm.contactPeople"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公司编码:" prop="companyCode">
                            <el-input v-model="ruleForm.companyCode" disabled v-if="ruleForm.exId"></el-input>
                            <el-input v-model="ruleForm.companyCode" v-else @blur="checkCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话:" prop="telephone">
                            <el-input v-model="ruleForm.telephone"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="管理账号:" prop="adminUser" >
                            <el-input v-model="ruleForm.adminUser" disabled v-if="ruleForm.exId"></el-input>
                            <el-input v-model="ruleForm.adminUser" v-else></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="授权开始时间:" prop="authorizeStartTime">
                            <el-date-picker v-model="ruleForm.authorizeStartTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="登录密码:" prop="adminPassword" >
                            <el-input v-model="ruleForm.adminPassword"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="授权结束时间:" prop="authorizeEndTime">
                            <el-date-picker v-model="ruleForm.authorizeEndTime" value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59" type="datetime" placeholder="选择日期" :picker-options="pickerOptions"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="统一社会信用代码:" prop="unifyCode" >
                            <el-input v-model="ruleForm.unifyCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所在国家:" prop="countryId" >
                            <country v-model="ruleForm.countryId" @change="country=>{[ruleForm.countryId,ruleForm.countryName]=country}" :countryId="ruleForm.countryId"></country>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                    <el-form-item label="公司地址:" prop="companyAddress">
                        <el-input v-model="ruleForm.companyAddress" style="width: 100%;max-width: 100%"></el-input>
                    </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="公司logo:" prop="companyLogo">
                            <el-upload
                                class="avatar-uploader"
                                action="/logManager/logistics/file/uploads"
                                name="files"
                                :headers="header"
                                :show-file-list="false"
                                :on-success="handleSuccess"
                                :on-error="handleError"
                                :on-progress="handleProgress"
                                :before-upload="beforeUpload">
                                <img v-if="ruleForm.companyLogo" :src="ruleForm.companyLogo" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
           <el-row v-if="log.length>0">
               <p class="title">异动记录</p>
               <el-table :data="log" border style="width: 100%">
                   <el-table-column align="center" label="序号" width="50" type="index"></el-table-column>
                   <el-table-column align="center" prop="type" label="异动类型" min-width="120"></el-table-column>
                   <el-table-column align="center" prop="remark" label="异动备注" min-width="300"></el-table-column>
                   <el-table-column align="center" prop="createUserName" label="操作人" min-width="100"></el-table-column>
                   <el-table-column align="center" prop="createTime" label="操作时间" min-width="120"></el-table-column>
               </el-table>
           </el-row>
        </div>
    </div>
</template>
<script>
    let token =  localStorage.getItem('adminToken');
    export default {
        data: function () {
            return {
                ruleForm: {
                    exId:'',
                    exName: '',
                    contactPeople: '',
                    companyCode: '',
                    telephone: '',
                    adminUser: '',
                    adminName: '',
                    adminPassword: '',
                    authorizeStartTime: '',
                    authorizeEndTime: '',
                    unifyCode: '',
                    companyAddress: '',
                    companyLogo: '',
                    countryId: '',
                    countryName: '',
                },
                rules: {
                    exName: [
                        { required: true, message: '请输入公司名称', trigger: 'blur' }
                    ],
                    adminUser: [
                        { required: true, message: '请输入管理账号', trigger: 'blur' }
                    ],
                    adminName: [
                        { required: true, message: '请输入联系人', trigger: 'blur' }
                    ],
                    adminPassword: [
                        { required: true, message: '请输入登录密码', trigger: 'blur' },
                        { pattern:/^.{6,}$/, message: '请输入正确的登录密码', trigger: 'blur' }
                    ],
                    telephone: [
                        { required: true, message: '请输入联系电话', trigger: 'blur' },
                        { pattern:/^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                    ],
                    companyCode: [
                        { required: true, message: '请输入公司编码', trigger: 'blur' }
                    ],
                    authorizeStartTime: [
                        { required: true, message: '请选择授权开始时间', trigger: 'change' }
                    ],
                    authorizeEndTime: [
                        { required: true, message: '请选择授权结束时间', trigger: 'change' }
                    ],
                    countryId: [
                        { required: true, message: '请选择所在国家', trigger: 'change' }
                    ],
                },
                log:[],//异动记录
                loading:'',
                header: {token: token},
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < Date.now();
                    },
                    shortcuts: [{
                        text: '一年',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24*365);
                            picker.$emit('pick', date);
                        }
                    }, {
                        text: '三年',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24*365*3);
                            picker.$emit('pick', date);
                        }
                    }]
                },
            }
        },
        mounted(){
            let myDate = new Date();
            this.ruleForm.startTime = myDate.getFullYear()+'-'+(myDate.getMonth()+1)+'-'+myDate.getDate();
            if(this.$route.query.id){
                this.ruleForm.exId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let url = '/logistics/couriercompany/info/'+this.ruleForm.exId;
                this.$axios.get(url).then((res) => {
                    if(res.code==0){
                        this.ruleForm = res.courierCompany;
                        this.getLog();
                    }
                });
            },
            getLog(){
                this.$axios.get('/logistics/couriercompany/listByCompany/'+this.ruleForm.exId).then((res) => {
                    if(res.code==0){
                        this.log = res.list;
                    }
                });
            },
            checkCode(){
                let self = this;
                if(self.ruleForm.companyCode=='')return;
                let url = '/logistics/couriercompany/findByCompanyCode/'+self.ruleForm.companyCode;
                self.$axios.get(url).then((res) => {
                    if(!res){
                        self.ruleForm.companyCode='';
                    }
                });
            },
            checkName(){
                let self = this;
                if(self.ruleForm.exName=='')return;
                let url = '/logistics/couriercompany/findByCompanyName/'+self.ruleForm.exName;
                self.$axios.get(url).then((res) => {
                    if(!res){
                        self.ruleForm.exName='';
                    }
                });
            },
            handleProgress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '正在上传图片...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            handleError(){
                this.loading.close();
                this.$message({
                    message: "上传失败,请重试",
                    type: 'error'
                })
            },
            handleSuccess(res, file) {
                this.loading.close();
                if(res.code!=0){
                    this.$message({
                        message: res.msg,
                        type: 'error'
                    })
                    if(res.code==401){
                        localStorage.setItem('adminToken','');
                        window.location.href = '/superadmin/#/login'
                    }
                }else{
                    this.ruleForm.companyLogo = res.result;
                }
            },
            beforeUpload(file) {
                const isJPG = (file.type).indexOf("image/")!=-1
                const isLt = file.size / 1024 / 1024 < 10;

                if (!isJPG) {
                    this.$message.error('请上传正确的图片格式!');
                }
                if (!isLt) {
                    this.$message.error('上传图片大小不能超过10MB!');
                }
                return isJPG && isLt;
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.exId?'/logistics/couriercompany/update':'/logistics/couriercompany/save';
                        let params = self.$util.extend(self.ruleForm);
                        params.adminUser = params.exId?self.ruleForm.adminUser:self.ruleForm.companyCode+self.ruleForm.adminUser;
                        self.$axios.post(url,params).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        }
    }
</script>
